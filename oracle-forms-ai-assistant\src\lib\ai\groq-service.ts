import Groq from 'groq-sdk';
import { 
  OracleFormsFile, 
  ParsedOracleFile, 
  AIAnalysis, 
  Finding, 
  Suggestion, 
  ChatMessage 
} from '@/types/oracle-forms';

export class GroqAIService {
  private groq: Groq;

  constructor(apiKey: string) {
    this.groq = new Groq({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true // Pour utilisation côté client
    });
  }

  async analyzeOracleFormsCode(
    file: OracleFormsFile, 
    parsedFile: ParsedOracleFile,
    analysisType: 'CODE_REVIEW' | 'DOCUMENTATION' | 'REFACTORING' | 'OPTIMIZATION'
  ): Promise<AIAnalysis> {
    const prompt = this.buildAnalysisPrompt(file, parsedFile, analysisType);
    
    try {
      const completion = await this.groq.chat.completions.create({
        messages: [
          {
            role: "system",
            content: this.getSystemPrompt(analysisType)
          },
          {
            role: "user",
            content: prompt
          }
        ],
        model: "llama-3.1-70b-versatile",
        temperature: 0.1,
        max_tokens: 4000
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('Aucune réponse reçue de l\'API Groq');
      }

      return this.parseAnalysisResponse(response, file.id, analysisType);
    } catch (error) {
      console.error('Erreur lors de l\'analyse avec Groq:', error);
      throw new Error('Échec de l\'analyse du code Oracle Forms');
    }
  }

  async generateDocumentation(
    file: OracleFormsFile, 
    parsedFile: ParsedOracleFile
  ): Promise<string> {
    const prompt = this.buildDocumentationPrompt(file, parsedFile);
    
    try {
      const completion = await this.groq.chat.completions.create({
        messages: [
          {
            role: "system",
            content: "Tu es un expert en Oracle Forms et en documentation technique. Génère une documentation complète et professionnelle en français pour le code Oracle Forms fourni."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        model: "llama-3.1-70b-versatile",
        temperature: 0.2,
        max_tokens: 6000
      });

      return completion.choices[0]?.message?.content || 'Erreur lors de la génération de la documentation';
    } catch (error) {
      console.error('Erreur lors de la génération de documentation:', error);
      throw new Error('Échec de la génération de documentation');
    }
  }

  async chatWithAssistant(
    messages: ChatMessage[],
    context?: { files: OracleFormsFile[], parsedFiles: ParsedOracleFile[] }
  ): Promise<string> {
    const systemPrompt = this.getChatSystemPrompt();
    const contextPrompt = context ? this.buildContextPrompt(context) : '';
    
    const groqMessages = [
      { role: "system" as const, content: systemPrompt + contextPrompt },
      ...messages.map(msg => ({
        role: msg.role as "user" | "assistant",
        content: msg.content
      }))
    ];

    try {
      const completion = await this.groq.chat.completions.create({
        messages: groqMessages,
        model: "llama-3.1-70b-versatile",
        temperature: 0.3,
        max_tokens: 3000
      });

      return completion.choices[0]?.message?.content || 'Désolé, je n\'ai pas pu traiter votre demande.';
    } catch (error) {
      console.error('Erreur lors du chat avec l\'assistant:', error);
      throw new Error('Échec de la communication avec l\'assistant IA');
    }
  }

  private getSystemPrompt(analysisType: string): string {
    const basePrompt = `Tu es un expert senior en Oracle Forms avec plus de 15 ans d'expérience. 
Tu maîtrises parfaitement PL/SQL, les meilleures pratiques de développement Oracle Forms, 
et les standards de codage bancaires. Tu réponds toujours en français.`;

    switch (analysisType) {
      case 'CODE_REVIEW':
        return `${basePrompt}
        
Effectue une revue de code approfondie en te concentrant sur :
- La qualité du code et les meilleures pratiques
- Les erreurs potentielles et les bugs
- La sécurité et les vulnérabilités
- La performance et l'optimisation
- La maintenabilité et la lisibilité
- La conformité aux standards Oracle Forms

Fournis tes résultats au format JSON avec les sections : summary, findings, suggestions.`;

      case 'DOCUMENTATION':
        return `${basePrompt}
        
Génère une documentation technique complète incluant :
- Vue d'ensemble du formulaire/rapport
- Description des blocs et items
- Explication des triggers et procédures
- Flux de données et logique métier
- Instructions d'utilisation
- Notes techniques importantes`;

      case 'REFACTORING':
        return `${basePrompt}
        
Analyse le code pour identifier les opportunités de refactorisation :
- Code dupliqué à factoriser
- Procédures trop longues à diviser
- Logique métier à centraliser
- Amélioration de la structure du code
- Simplification des expressions complexes`;

      case 'OPTIMIZATION':
        return `${basePrompt}
        
Identifie les optimisations possibles :
- Performance des requêtes SQL
- Utilisation efficace de la mémoire
- Optimisation des triggers
- Réduction des appels réseau
- Amélioration de l'expérience utilisateur`;

      default:
        return basePrompt;
    }
  }

  private getChatSystemPrompt(): string {
    return `Tu es un assistant IA expert en Oracle Forms, spécialisé dans l'aide aux développeurs. 
Tu peux :
- Analyser et expliquer le code Oracle Forms (FMB, RDF, PLL, OLB)
- Suggérer des améliorations et corrections
- Répondre aux questions techniques
- Aider à la résolution de problèmes
- Fournir des exemples de code
- Expliquer les meilleures pratiques

Tu réponds toujours en français, de manière claire et professionnelle.
Si tu ne comprends pas une question, demande des clarifications.
Utilise le contexte des fichiers fournis pour donner des réponses précises.`;
  }

  private buildAnalysisPrompt(
    file: OracleFormsFile, 
    parsedFile: ParsedOracleFile, 
    analysisType: string
  ): string {
    return `Analyse ce fichier Oracle Forms ${file.type} :

**Nom du fichier :** ${file.name}
**Type :** ${file.type}
**Taille :** ${file.size} octets

**Métadonnées :**
- Version : ${parsedFile.metadata.version}
- Nom du formulaire : ${parsedFile.metadata.formName || 'N/A'}
- Description : ${parsedFile.metadata.description || 'N/A'}

**Structure :**
- Blocs : ${parsedFile.blocks.length}
- Triggers : ${parsedFile.triggers.length}
- Procédures : ${parsedFile.procedures.length}
- Fonctions : ${parsedFile.functions.length}
- Variables : ${parsedFile.variables.length}

**Contenu du code (extrait) :**
\`\`\`plsql
${file.content.substring(0, 2000)}${file.content.length > 2000 ? '...' : ''}
\`\`\`

Type d'analyse demandée : ${analysisType}`;
  }

  private buildDocumentationPrompt(
    file: OracleFormsFile, 
    parsedFile: ParsedOracleFile
  ): string {
    return `Génère une documentation technique complète pour ce fichier Oracle Forms :

**Fichier :** ${file.name} (${file.type})

**Éléments à documenter :**

**Blocs (${parsedFile.blocks.length}) :**
${parsedFile.blocks.map(block => `- ${block.name} (${block.type})`).join('\n')}

**Triggers (${parsedFile.triggers.length}) :**
${parsedFile.triggers.map(trigger => `- ${trigger.name} (${trigger.level})`).join('\n')}

**Procédures (${parsedFile.procedures.length}) :**
${parsedFile.procedures.map(proc => `- ${proc.name}`).join('\n')}

**Code source :**
\`\`\`plsql
${file.content}
\`\`\``;
  }

  private buildContextPrompt(context: { files: OracleFormsFile[], parsedFiles: ParsedOracleFile[] }): string {
    if (!context.files.length) return '';

    return `\n\nContexte des fichiers chargés :
${context.files.map((file, index) => `
**${file.name}** (${file.type})
- Blocs : ${context.parsedFiles[index]?.blocks.length || 0}
- Triggers : ${context.parsedFiles[index]?.triggers.length || 0}
- Procédures : ${context.parsedFiles[index]?.procedures.length || 0}
`).join('')}`;
  }

  private parseAnalysisResponse(response: string, fileId: string, analysisType: string): AIAnalysis {
    try {
      // Tentative de parsing JSON
      const parsed = JSON.parse(response);
      
      return {
        id: `analysis_${Date.now()}`,
        fileId,
        timestamp: new Date(),
        type: analysisType as any,
        summary: parsed.summary || 'Analyse terminée',
        findings: parsed.findings || [],
        suggestions: parsed.suggestions || [],
        score: parsed.score
      };
    } catch {
      // Si le parsing JSON échoue, créer une analyse basique
      return {
        id: `analysis_${Date.now()}`,
        fileId,
        timestamp: new Date(),
        type: analysisType as any,
        summary: response.substring(0, 200) + '...',
        findings: [],
        suggestions: [],
      };
    }
  }
}
