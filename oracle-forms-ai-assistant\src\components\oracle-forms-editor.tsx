'use client';

import { useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { FileUploader } from '@/components/file-uploader';
import { CodeEditor } from '@/components/code-editor';
import { AIChat } from '@/components/ai-chat';
import { AnalysisPanel } from '@/components/analysis-panel';
import { OracleFormsFile, ParsedOracleFile, AIAnalysis } from '@/types/oracle-forms';
import { FMBParser } from '@/lib/parsers/fmb-parser';
import { GroqAIService } from '@/lib/ai/groq-service';

export function OracleFormsEditor() {
  const [files, setFiles] = useState<OracleFormsFile[]>([]);
  const [parsedFiles, setParsedFiles] = useState<ParsedOracleFile[]>([]);
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [analyses, setAnalyses] = useState<AIAnalysis[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [groqApiKey, setGroqApiKey] = useState<string>('');

  const selectedFile = files.find(f => f.id === selectedFileId);
  const selectedParsedFile = parsedFiles.find(f => 
    files.find(file => file.id === selectedFileId)?.id === selectedFileId
  );

  const handleFilesUploaded = useCallback(async (uploadedFiles: OracleFormsFile[]) => {
    setFiles(prev => [...prev, ...uploadedFiles]);
    
    // Parse les nouveaux fichiers
    const newParsedFiles: ParsedOracleFile[] = [];
    
    for (const file of uploadedFiles) {
      try {
        let parser;
        switch (file.type) {
          case 'FMB':
            parser = new FMBParser(file);
            break;
          // TODO: Ajouter d'autres parsers pour RDF, PLL, OLB
          default:
            console.warn(`Parser non implémenté pour le type ${file.type}`);
            continue;
        }
        
        const parsed = await parser.parse();
        newParsedFiles.push(parsed);
      } catch (error) {
        console.error(`Erreur lors du parsing de ${file.name}:`, error);
      }
    }
    
    setParsedFiles(prev => [...prev, ...newParsedFiles]);
    
    // Sélectionner le premier fichier si aucun n'est sélectionné
    if (!selectedFileId && uploadedFiles.length > 0) {
      setSelectedFileId(uploadedFiles[0].id);
    }
  }, [selectedFileId]);

  const handleAnalyzeFile = useCallback(async (
    fileId: string, 
    analysisType: 'CODE_REVIEW' | 'DOCUMENTATION' | 'REFACTORING' | 'OPTIMIZATION'
  ) => {
    if (!groqApiKey) {
      alert('Veuillez configurer votre clé API Groq dans les paramètres');
      return;
    }

    const file = files.find(f => f.id === fileId);
    const parsedFile = parsedFiles.find((_, index) => files[index]?.id === fileId);
    
    if (!file || !parsedFile) {
      console.error('Fichier ou données parsées non trouvés');
      return;
    }

    setIsAnalyzing(true);
    
    try {
      const aiService = new GroqAIService(groqApiKey);
      const analysis = await aiService.analyzeOracleFormsCode(file, parsedFile, analysisType);
      
      setAnalyses(prev => [...prev, analysis]);
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
      alert('Erreur lors de l\'analyse du fichier');
    } finally {
      setIsAnalyzing(false);
    }
  }, [files, parsedFiles, groqApiKey]);

  const handleGenerateDocumentation = useCallback(async (fileId: string) => {
    if (!groqApiKey) {
      alert('Veuillez configurer votre clé API Groq dans les paramètres');
      return;
    }

    const file = files.find(f => f.id === fileId);
    const parsedFile = parsedFiles.find((_, index) => files[index]?.id === fileId);
    
    if (!file || !parsedFile) {
      console.error('Fichier ou données parsées non trouvés');
      return;
    }

    setIsAnalyzing(true);
    
    try {
      const aiService = new GroqAIService(groqApiKey);
      const documentation = await aiService.generateDocumentation(file, parsedFile);
      
      // TODO: Afficher la documentation dans un modal ou un nouvel onglet
      console.log('Documentation générée:', documentation);
      alert('Documentation générée avec succès ! Consultez la console pour voir le résultat.');
    } catch (error) {
      console.error('Erreur lors de la génération de documentation:', error);
      alert('Erreur lors de la génération de documentation');
    } finally {
      setIsAnalyzing(false);
    }
  }, [files, parsedFiles, groqApiKey]);

  return (
    <div className="h-[calc(100vh-120px)] flex flex-col">
      {/* Configuration API Key */}
      {!groqApiKey && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-yellow-800">
                Configuration requise
              </h3>
              <p className="text-sm text-yellow-700 mt-1">
                Veuillez configurer votre clé API Groq pour utiliser les fonctionnalités IA.
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="password"
                placeholder="Clé API Groq"
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                onChange={(e) => setGroqApiKey(e.target.value)}
              />
            </div>
          </div>
        </div>
      )}

      <Tabs defaultValue="files" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="files">Fichiers</TabsTrigger>
          <TabsTrigger value="editor">Éditeur</TabsTrigger>
          <TabsTrigger value="analysis">Analyse</TabsTrigger>
          <TabsTrigger value="chat">Assistant IA</TabsTrigger>
        </TabsList>

        <TabsContent value="files" className="flex-1">
          <FileUploader 
            onFilesUploaded={handleFilesUploaded}
            files={files}
            onFileSelect={setSelectedFileId}
            selectedFileId={selectedFileId}
            onAnalyzeFile={handleAnalyzeFile}
            onGenerateDocumentation={handleGenerateDocumentation}
            isAnalyzing={isAnalyzing}
          />
        </TabsContent>

        <TabsContent value="editor" className="flex-1">
          {selectedFile ? (
            <CodeEditor 
              file={selectedFile}
              parsedFile={selectedParsedFile}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <p>Sélectionnez un fichier pour commencer l'édition</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="analysis" className="flex-1">
          <AnalysisPanel 
            analyses={analyses}
            files={files}
          />
        </TabsContent>

        <TabsContent value="chat" className="flex-1">
          <AIChat 
            files={files}
            parsedFiles={parsedFiles}
            groqApiKey={groqApiKey}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
