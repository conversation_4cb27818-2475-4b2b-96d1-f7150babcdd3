# Oracle Forms AI Assistant

Un assistant IA professionnel pour l'analyse, la documentation et l'amélioration du code Oracle Forms, développé avec Next.js et l'API Groq.

## 🎯 Objectifs du projet

Développer un assistant IA capable de :
- Analyser, commenter et suggérer des améliorations pour le code Oracle Forms (FMB), Reports (RDF), bibliothèques (PLL) et objets (OLB)
- Fournir des explications détaillées sur le code, des commentaires, des revues de code et des suggestions de refactorisation
- Permettre l'insertion des modifications dans le code avec l'approbation du développeur
- Offrir une interface de chat pour des interactions en langage naturel avec l'assistant IA

## 🧠 Capacités de l'assistant IA

### Analyse de code Oracle Forms et Reports
- Chargement et parsing des fichiers FMB, RDF, PLL et OLB
- Extraction des blocs, triggers, objets, bibliothèques et dépendances
- Identification du code redondant, des objets obsolètes et des triggers mal positionnés

### Documentation et commentaires
- Génération de commentaires explicatifs pour chaque module
- Création de documentation technique à partir du code existant
- Résumé des fonctions et procédures en langage naturel

### Revue et refactorisation de code
- Suggestions d'améliorations basées sur les meilleures pratiques en PL/SQL
- Détection des anomalies, des erreurs potentielles et des violations des standards de codage
- Proposition de refactorisation pour améliorer la maintenabilité et la performance

### Interaction en langage naturel
- Interface de chat pour poser des questions sur le code, demander des explications ou des modifications
- Compréhension des requêtes contextuelles et réponse adaptée aux besoins du développeur

## 🛠️ Technologies utilisées

- **Frontend** : Next.js 15 avec TypeScript
- **UI** : Tailwind CSS, Radix UI
- **Éditeur de code** : Monaco Editor
- **IA** : API Groq avec modèle Llama 3.1 70B
- **Parsing** : Parsers personnalisés pour Oracle Forms

## 🚀 Installation et démarrage

### Prérequis
- Node.js 18+
- npm ou yarn
- Clé API Groq (gratuite sur [console.groq.com](https://console.groq.com))

### Installation

```bash
# Cloner le projet
git clone <repository-url>
cd oracle-forms-ai-assistant

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm run dev
```

Ouvrir [http://localhost:3000](http://localhost:3000) dans votre navigateur.

### Configuration

1. **Clé API Groq** : Obtenez une clé API gratuite sur [console.groq.com](https://console.groq.com)
2. **Configuration dans l'app** : Entrez votre clé API dans le champ prévu à cet effet dans l'interface

## 📁 Structure du projet

```
oracle-forms-ai-assistant/
├── src/
│   ├── app/                    # Pages Next.js
│   ├── components/             # Composants React
│   │   ├── ui/                # Composants UI de base
│   │   ├── oracle-forms-editor.tsx
│   │   ├── file-uploader.tsx
│   │   ├── code-editor.tsx
│   │   ├── analysis-panel.tsx
│   │   └── ai-chat.tsx
│   ├── lib/                   # Utilitaires et services
│   │   ├── parsers/           # Parsers Oracle Forms
│   │   ├── ai/                # Services IA
│   │   └── utils.ts
│   └── types/                 # Types TypeScript
└── public/                    # Fichiers statiques
```

## 🎮 Utilisation

### 1. Chargement des fichiers
- Glissez-déposez vos fichiers Oracle Forms (.fmb, .rdf, .pll, .olb) dans la zone prévue
- Ou cliquez pour sélectionner des fichiers

### 2. Analyse du code
- Sélectionnez un fichier dans la liste
- Cliquez sur "Analyser" pour lancer une revue de code IA
- Consultez les résultats dans l'onglet "Analyse"

### 3. Édition du code
- Utilisez l'onglet "Éditeur" pour visualiser et modifier le code
- Explorez la structure, les triggers et les procédures

### 4. Chat avec l'assistant IA
- Utilisez l'onglet "Assistant IA" pour poser des questions
- L'assistant a accès au contexte de vos fichiers chargés

## 🔧 Fonctionnalités

### ✅ Implémentées
- [x] Interface utilisateur moderne avec Next.js et Tailwind CSS
- [x] Chargement et parsing des fichiers FMB
- [x] Éditeur de code avec coloration syntaxique (Monaco Editor)
- [x] Intégration API Groq pour l'analyse IA
- [x] Chat interactif avec l'assistant IA
- [x] Analyse de code et génération de suggestions
- [x] Interface d'analyse avec visualisation des problèmes

### 🚧 En développement
- [ ] Parsers pour RDF, PLL et OLB
- [ ] Sauvegarde des modifications
- [ ] Export de la documentation générée
- [ ] Historique des analyses
- [ ] Authentification utilisateur
- [ ] Intégration avec systèmes de contrôle de version

## 🤝 Contribution

Les contributions sont les bienvenues ! Pour contribuer :

1. Fork le projet
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour obtenir de l'aide :
- Ouvrez une issue sur GitHub
- Consultez la documentation
- Contactez l'équipe de développement

## 🔐 Sécurité

- Les clés API sont stockées localement dans le navigateur
- Aucune donnée n'est envoyée à des serveurs tiers sans votre consentement
- Le code source est analysé localement avant envoi à l'API IA
