{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/components/file-uploader.tsx"], "sourcesContent": ["'use client';\n\nimport { useCallback, useState } from 'react';\nimport { Upload, File, Play, FileText, Loader2 } from 'lucide-react';\nimport { OracleFormsFile } from '@/types/oracle-forms';\n\ninterface FileUploaderProps {\n  onFilesUploaded: (files: OracleFormsFile[]) => void;\n  files: OracleFormsFile[];\n  onFileSelect: (fileId: string) => void;\n  selectedFileId: string | null;\n  onAnalyzeFile: (fileId: string, analysisType: 'CODE_REVIEW' | 'DOCUMENTATION' | 'REFACTORING' | 'OPTIMIZATION') => void;\n  onGenerateDocumentation: (fileId: string) => void;\n  isAnalyzing: boolean;\n}\n\nexport function FileUploader({\n  onFilesUploaded,\n  files,\n  onFileSelect,\n  selectedFileId,\n  onAnalyzeFile,\n  onGenerateDocumentation,\n  isAnalyzing\n}: FileUploaderProps) {\n  const [dragActive, setDragActive] = useState(false);\n\n  const handleDrag = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === \"dragenter\" || e.type === \"dragover\") {\n      setDragActive(true);\n    } else if (e.type === \"dragleave\") {\n      setDragActive(false);\n    }\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleFiles(e.dataTransfer.files);\n    }\n  }, []);\n\n  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    e.preventDefault();\n    if (e.target.files && e.target.files[0]) {\n      handleFiles(e.target.files);\n    }\n  }, []);\n\n  const handleFiles = useCallback(async (fileList: FileList) => {\n    const newFiles: OracleFormsFile[] = [];\n    \n    for (let i = 0; i < fileList.length; i++) {\n      const file = fileList[i];\n      const extension = file.name.split('.').pop()?.toUpperCase();\n      \n      if (!['FMB', 'RDF', 'PLL', 'OLB'].includes(extension || '')) {\n        alert(`Type de fichier non supporté: ${file.name}`);\n        continue;\n      }\n      \n      try {\n        const content = await file.text();\n        const oracleFile: OracleFormsFile = {\n          id: `${Date.now()}_${i}`,\n          name: file.name,\n          type: extension as 'FMB' | 'RDF' | 'PLL' | 'OLB',\n          content,\n          size: file.size,\n          lastModified: new Date(file.lastModified)\n        };\n        \n        newFiles.push(oracleFile);\n      } catch (error) {\n        console.error(`Erreur lors de la lecture du fichier ${file.name}:`, error);\n        alert(`Erreur lors de la lecture du fichier ${file.name}`);\n      }\n    }\n    \n    if (newFiles.length > 0) {\n      onFilesUploaded(newFiles);\n    }\n  }, [onFilesUploaded]);\n\n  const getFileIcon = (type: string) => {\n    switch (type) {\n      case 'FMB':\n        return '📋';\n      case 'RDF':\n        return '📊';\n      case 'PLL':\n        return '📚';\n      case 'OLB':\n        return '🔧';\n      default:\n        return '📄';\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Zone de drop */}\n      <div\n        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n          dragActive\n            ? 'border-blue-400 bg-blue-50'\n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n        onDragEnter={handleDrag}\n        onDragLeave={handleDrag}\n        onDragOver={handleDrag}\n        onDrop={handleDrop}\n      >\n        <input\n          type=\"file\"\n          multiple\n          accept=\".fmb,.rdf,.pll,.olb\"\n          onChange={handleChange}\n          className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n        />\n        \n        <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n          Glissez-déposez vos fichiers Oracle Forms\n        </h3>\n        <p className=\"text-sm text-gray-500 mb-4\">\n          Ou cliquez pour sélectionner des fichiers\n        </p>\n        <p className=\"text-xs text-gray-400\">\n          Formats supportés: FMB, RDF, PLL, OLB\n        </p>\n      </div>\n\n      {/* Liste des fichiers */}\n      {files.length > 0 && (\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            Fichiers chargés ({files.length})\n          </h3>\n          \n          <div className=\"grid gap-4\">\n            {files.map((file) => (\n              <div\n                key={file.id}\n                className={`border rounded-lg p-4 cursor-pointer transition-colors ${\n                  selectedFileId === file.id\n                    ? 'border-blue-500 bg-blue-50'\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n                onClick={() => onFileSelect(file.id)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-2xl\">{getFileIcon(file.type)}</span>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">{file.name}</h4>\n                      <p className=\"text-sm text-gray-500\">\n                        {file.type} • {formatFileSize(file.size)} • \n                        Modifié le {file.lastModified.toLocaleDateString()}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        onAnalyzeFile(file.id, 'CODE_REVIEW');\n                      }}\n                      disabled={isAnalyzing}\n                      className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50\"\n                    >\n                      {isAnalyzing ? (\n                        <Loader2 className=\"h-3 w-3 animate-spin mr-1\" />\n                      ) : (\n                        <Play className=\"h-3 w-3 mr-1\" />\n                      )}\n                      Analyser\n                    </button>\n                    \n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        onGenerateDocumentation(file.id);\n                      }}\n                      disabled={isAnalyzing}\n                      className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 disabled:opacity-50\"\n                    >\n                      {isAnalyzing ? (\n                        <Loader2 className=\"h-3 w-3 animate-spin mr-1\" />\n                      ) : (\n                        <FileText className=\"h-3 w-3 mr-1\" />\n                      )}\n                      Documenter\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAgBO,SAAS,aAAa,EAC3B,eAAe,EACf,KAAK,EACL,YAAY,EACZ,cAAc,EACd,aAAa,EACb,uBAAuB,EACvB,WAAW,EACO;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,YAAY,EAAE,YAAY,CAAC,KAAK;QAClC;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,EAAE,cAAc;QAChB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,YAAY,EAAE,MAAM,CAAC,KAAK;QAC5B;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,MAAM,WAA8B,EAAE;QAEtC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,MAAM,OAAO,QAAQ,CAAC,EAAE;YACxB,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;YAE9C,IAAI,CAAC;gBAAC;gBAAO;gBAAO;gBAAO;aAAM,CAAC,QAAQ,CAAC,aAAa,KAAK;gBAC3D,MAAM,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;gBAClD;YACF;YAEA,IAAI;gBACF,MAAM,UAAU,MAAM,KAAK,IAAI;gBAC/B,MAAM,aAA8B;oBAClC,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG;oBACxB,MAAM,KAAK,IAAI;oBACf,MAAM;oBACN;oBACA,MAAM,KAAK,IAAI;oBACf,cAAc,IAAI,KAAK,KAAK,YAAY;gBAC1C;gBAEA,SAAS,IAAI,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;gBACpE,MAAM,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YAC3D;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,CAAC,6EAA6E,EACvF,aACI,+BACA,yCACJ;gBACF,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;;kCAER,8OAAC;wBACC,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;YAMtC,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoC;4BAC7B,MAAM,MAAM;4BAAC;;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gCAEC,WAAW,CAAC,uDAAuD,EACjE,mBAAmB,KAAK,EAAE,GACtB,+BACA,yCACJ;gCACF,SAAS,IAAM,aAAa,KAAK,EAAE;0CAEnC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAY,YAAY,KAAK,IAAI;;;;;;8DACjD,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6B,KAAK,IAAI;;;;;;sEACpD,8OAAC;4DAAE,WAAU;;gEACV,KAAK,IAAI;gEAAC;gEAAI,eAAe,KAAK,IAAI;gEAAE;gEAC7B,KAAK,YAAY,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;sDAKtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,cAAc,KAAK,EAAE,EAAE;oDACzB;oDACA,UAAU;oDACV,WAAU;;wDAET,4BACC,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAChB;;;;;;;8DAIJ,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,wBAAwB,KAAK,EAAE;oDACjC;oDACA,UAAU;oDACV,WAAU;;wDAET,4BACC,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACpB;;;;;;;;;;;;;;;;;;;+BAjDH,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AA6D5B", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/components/code-editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Editor from '@monaco-editor/react';\nimport { OracleFormsFile, ParsedOracleFile } from '@/types/oracle-forms';\nimport { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';\n\ninterface CodeEditorProps {\n  file: OracleFormsFile;\n  parsedFile?: ParsedOracleFile;\n}\n\nexport function CodeEditor({ file, parsedFile }: CodeEditorProps) {\n  const [code, setCode] = useState(file.content);\n  const [selectedTab, setSelectedTab] = useState('code');\n\n  useEffect(() => {\n    setCode(file.content);\n  }, [file.content]);\n\n  const handleEditorChange = (value: string | undefined) => {\n    if (value !== undefined) {\n      setCode(value);\n    }\n  };\n\n  const getLanguage = (fileType: string) => {\n    switch (fileType) {\n      case 'FMB':\n      case 'RDF':\n      case 'PLL':\n        return 'sql'; // PL/SQL est proche de SQL pour la coloration syntaxique\n      case 'OLB':\n        return 'sql';\n      default:\n        return 'plaintext';\n    }\n  };\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      <div className=\"flex items-center justify-between p-4 border-b bg-white\">\n        <div>\n          <h2 className=\"text-lg font-semibold text-gray-900\">{file.name}</h2>\n          <p className=\"text-sm text-gray-500\">\n            {file.type} • {Math.round(file.size / 1024)} KB\n          </p>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <span className=\"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded\">\n            {file.type}\n          </span>\n          <button className=\"px-3 py-1 text-sm font-medium text-blue-600 hover:text-blue-800\">\n            Sauvegarder\n          </button>\n        </div>\n      </div>\n\n      <Tabs value={selectedTab} onValueChange={setSelectedTab} className=\"flex-1 flex flex-col\">\n        <TabsList className=\"w-full justify-start border-b rounded-none bg-gray-50\">\n          <TabsTrigger value=\"code\">Code Source</TabsTrigger>\n          {parsedFile && (\n            <>\n              <TabsTrigger value=\"structure\">Structure</TabsTrigger>\n              <TabsTrigger value=\"triggers\">Triggers</TabsTrigger>\n              <TabsTrigger value=\"procedures\">Procédures</TabsTrigger>\n            </>\n          )}\n        </TabsList>\n\n        <TabsContent value=\"code\" className=\"flex-1 mt-0\">\n          <Editor\n            height=\"100%\"\n            language={getLanguage(file.type)}\n            value={code}\n            onChange={handleEditorChange}\n            theme=\"vs-light\"\n            options={{\n              minimap: { enabled: true },\n              fontSize: 14,\n              lineNumbers: 'on',\n              wordWrap: 'on',\n              automaticLayout: true,\n              scrollBeyondLastLine: false,\n              readOnly: false,\n              folding: true,\n              lineDecorationsWidth: 10,\n              lineNumbersMinChars: 4,\n              glyphMargin: true,\n              contextmenu: true,\n              mouseWheelZoom: true,\n              cursorBlinking: 'blink',\n              cursorStyle: 'line',\n              renderWhitespace: 'selection',\n              renderControlCharacters: false,\n              fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n              tabSize: 2,\n              insertSpaces: true,\n              detectIndentation: true,\n              trimAutoWhitespace: true,\n              formatOnPaste: true,\n              formatOnType: true\n            }}\n          />\n        </TabsContent>\n\n        {parsedFile && (\n          <>\n            <TabsContent value=\"structure\" className=\"flex-1 mt-0 p-4 overflow-auto\">\n              <div className=\"space-y-6\">\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-3\">Métadonnées</h3>\n                  <div className=\"bg-gray-50 rounded-lg p-4 space-y-2\">\n                    <div><strong>Version:</strong> {parsedFile.metadata.version}</div>\n                    {parsedFile.metadata.formName && (\n                      <div><strong>Nom du formulaire:</strong> {parsedFile.metadata.formName}</div>\n                    )}\n                    {parsedFile.metadata.description && (\n                      <div><strong>Description:</strong> {parsedFile.metadata.description}</div>\n                    )}\n                    {parsedFile.metadata.author && (\n                      <div><strong>Auteur:</strong> {parsedFile.metadata.author}</div>\n                    )}\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-3\">Blocs ({parsedFile.blocks.length})</h3>\n                  <div className=\"space-y-3\">\n                    {parsedFile.blocks.map((block) => (\n                      <div key={block.id} className=\"border rounded-lg p-4\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"font-medium\">{block.name}</h4>\n                          <span className={`px-2 py-1 text-xs rounded ${\n                            block.type === 'DATA_BLOCK' \n                              ? 'bg-blue-100 text-blue-800' \n                              : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {block.type}\n                          </span>\n                        </div>\n                        {block.tableName && (\n                          <p className=\"text-sm text-gray-600 mb-2\">\n                            Table: {block.tableName}\n                          </p>\n                        )}\n                        <p className=\"text-sm text-gray-500\">\n                          {block.items.length} items, {block.triggers.length} triggers\n                        </p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-3\">Dépendances ({parsedFile.dependencies.length})</h3>\n                  <div className=\"space-y-2\">\n                    {parsedFile.dependencies.map((dep) => (\n                      <div key={dep.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                        <span className=\"font-medium\">{dep.name}</span>\n                        <span className={`px-2 py-1 text-xs rounded ${\n                          dep.required ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'\n                        }`}>\n                          {dep.required ? 'Requis' : 'Optionnel'}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"triggers\" className=\"flex-1 mt-0 p-4 overflow-auto\">\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold\">Triggers ({parsedFile.triggers.length})</h3>\n                {parsedFile.triggers.map((trigger) => (\n                  <div key={trigger.id} className=\"border rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium\">{trigger.name}</h4>\n                      <span className=\"px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded\">\n                        {trigger.level}\n                      </span>\n                    </div>\n                    <pre className=\"bg-gray-50 p-3 rounded text-sm overflow-x-auto\">\n                      <code>{trigger.code}</code>\n                    </pre>\n                  </div>\n                ))}\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"procedures\" className=\"flex-1 mt-0 p-4 overflow-auto\">\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold\">\n                  Procédures et Fonctions ({parsedFile.procedures.length + parsedFile.functions.length})\n                </h3>\n                \n                {parsedFile.procedures.map((proc) => (\n                  <div key={proc.id} className=\"border rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium\">{proc.name}</h4>\n                      <span className=\"px-2 py-1 text-xs bg-green-100 text-green-800 rounded\">\n                        PROCEDURE\n                      </span>\n                    </div>\n                    {proc.parameters.length > 0 && (\n                      <div className=\"mb-2\">\n                        <strong className=\"text-sm\">Paramètres:</strong>\n                        <ul className=\"text-sm text-gray-600 ml-4\">\n                          {proc.parameters.map((param, index) => (\n                            <li key={index}>\n                              {param.name} ({param.mode}) {param.type}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                    <pre className=\"bg-gray-50 p-3 rounded text-sm overflow-x-auto\">\n                      <code>{proc.code}</code>\n                    </pre>\n                  </div>\n                ))}\n\n                {parsedFile.functions.map((func) => (\n                  <div key={func.id} className=\"border rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium\">{func.name}</h4>\n                      <span className=\"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\">\n                        FUNCTION → {func.returnType}\n                      </span>\n                    </div>\n                    {func.parameters.length > 0 && (\n                      <div className=\"mb-2\">\n                        <strong className=\"text-sm\">Paramètres:</strong>\n                        <ul className=\"text-sm text-gray-600 ml-4\">\n                          {func.parameters.map((param, index) => (\n                            <li key={index}>\n                              {param.name} ({param.mode}) {param.type}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    )}\n                    <pre className=\"bg-gray-50 p-3 rounded text-sm overflow-x-auto\">\n                      <code>{func.code}</code>\n                    </pre>\n                  </div>\n                ))}\n              </div>\n            </TabsContent>\n          </>\n        )}\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AALA;;;;;AAYO,SAAS,WAAW,EAAE,IAAI,EAAE,UAAU,EAAmB;IAC9D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,OAAO;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,KAAK,OAAO;IACtB,GAAG;QAAC,KAAK,OAAO;KAAC;IAEjB,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,WAAW;YACvB,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;g<PERSON><PERSON>,OAAO,OAAO,yDAAyD;YACzE,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAuC,KAAK,IAAI;;;;;;0CAC9D,8OAAC;gCAAE,WAAU;;oCACV,KAAK,IAAI;oCAAC;oCAAI,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG;oCAAM;;;;;;;;;;;;;kCAIhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,KAAK,IAAI;;;;;;0CAEZ,8OAAC;gCAAO,WAAU;0CAAkE;;;;;;;;;;;;;;;;;;0BAMxF,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAa,eAAe;gBAAgB,WAAU;;kCACjE,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;4BACzB,4BACC;;kDACE,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAY;;;;;;kDAC/B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAa;;;;;;;;;;;;;;kCAKtC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;kCAClC,cAAA,8OAAC,6KAAA,CAAA,UAAM;4BACL,QAAO;4BACP,UAAU,YAAY,KAAK,IAAI;4BAC/B,OAAO;4BACP,UAAU;4BACV,OAAM;4BACN,SAAS;gCACP,SAAS;oCAAE,SAAS;gCAAK;gCACzB,UAAU;gCACV,aAAa;gCACb,UAAU;gCACV,iBAAiB;gCACjB,sBAAsB;gCACtB,UAAU;gCACV,SAAS;gCACT,sBAAsB;gCACtB,qBAAqB;gCACrB,aAAa;gCACb,aAAa;gCACb,gBAAgB;gCAChB,gBAAgB;gCAChB,aAAa;gCACb,kBAAkB;gCAClB,yBAAyB;gCACzB,YAAY;gCACZ,SAAS;gCACT,cAAc;gCACd,mBAAmB;gCACnB,oBAAoB;gCACpB,eAAe;gCACf,cAAc;4BAChB;;;;;;;;;;;oBAIH,4BACC;;0CACE,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAAiB;gEAAE,WAAW,QAAQ,CAAC,OAAO;;;;;;;wDAC1D,WAAW,QAAQ,CAAC,QAAQ,kBAC3B,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAA2B;gEAAE,WAAW,QAAQ,CAAC,QAAQ;;;;;;;wDAEvE,WAAW,QAAQ,CAAC,WAAW,kBAC9B,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAAqB;gEAAE,WAAW,QAAQ,CAAC,WAAW;;;;;;;wDAEpE,WAAW,QAAQ,CAAC,MAAM,kBACzB,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAAgB;gEAAE,WAAW,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;;;sDAK/D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDAA6B;wDAAQ,WAAW,MAAM,CAAC,MAAM;wDAAC;;;;;;;8DAC5E,8OAAC;oDAAI,WAAU;8DACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,sBACtB,8OAAC;4DAAmB,WAAU;;8EAC5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAe,MAAM,IAAI;;;;;;sFACvC,8OAAC;4EAAK,WAAW,CAAC,0BAA0B,EAC1C,MAAM,IAAI,KAAK,eACX,8BACA,6BACJ;sFACC,MAAM,IAAI;;;;;;;;;;;;gEAGd,MAAM,SAAS,kBACd,8OAAC;oEAAE,WAAU;;wEAA6B;wEAChC,MAAM,SAAS;;;;;;;8EAG3B,8OAAC;oEAAE,WAAU;;wEACV,MAAM,KAAK,CAAC,MAAM;wEAAC;wEAAS,MAAM,QAAQ,CAAC,MAAM;wEAAC;;;;;;;;2DAjB7C,MAAM,EAAE;;;;;;;;;;;;;;;;sDAwBxB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDAA6B;wDAAc,WAAW,YAAY,CAAC,MAAM;wDAAC;;;;;;;8DACxF,8OAAC;oDAAI,WAAU;8DACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,oBAC5B,8OAAC;4DAAiB,WAAU;;8EAC1B,8OAAC;oEAAK,WAAU;8EAAe,IAAI,IAAI;;;;;;8EACvC,8OAAC;oEAAK,WAAW,CAAC,0BAA0B,EAC1C,IAAI,QAAQ,GAAG,4BAA4B,+BAC3C;8EACC,IAAI,QAAQ,GAAG,WAAW;;;;;;;2DALrB,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAc1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAwB;gDAAW,WAAW,QAAQ,CAAC,MAAM;gDAAC;;;;;;;wCAC3E,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACxB,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAe,QAAQ,IAAI;;;;;;0EACzC,8OAAC;gEAAK,WAAU;0EACb,QAAQ,KAAK;;;;;;;;;;;;kEAGlB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;sEAAM,QAAQ,IAAI;;;;;;;;;;;;+CARb,QAAQ,EAAE;;;;;;;;;;;;;;;;0CAe1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;0CACxC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAwB;gDACV,WAAW,UAAU,CAAC,MAAM,GAAG,WAAW,SAAS,CAAC,MAAM;gDAAC;;;;;;;wCAGtF,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;gDAAkB,WAAU;;kEAC3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAe,KAAK,IAAI;;;;;;0EACtC,8OAAC;gEAAK,WAAU;0EAAwD;;;;;;;;;;;;oDAIzE,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAAU;;;;;;0EAC5B,8OAAC;gEAAG,WAAU;0EACX,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;;4EACE,MAAM,IAAI;4EAAC;4EAAG,MAAM,IAAI;4EAAC;4EAAG,MAAM,IAAI;;uEADhC;;;;;;;;;;;;;;;;kEAOjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;sEAAM,KAAK,IAAI;;;;;;;;;;;;+CApBV,KAAK,EAAE;;;;;wCAyBlB,WAAW,SAAS,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;gDAAkB,WAAU;;kEAC3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAe,KAAK,IAAI;;;;;;0EACtC,8OAAC;gEAAK,WAAU;;oEAAsD;oEACxD,KAAK,UAAU;;;;;;;;;;;;;oDAG9B,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAAU;;;;;;0EAC5B,8OAAC;gEAAG,WAAU;0EACX,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;;4EACE,MAAM,IAAI;4EAAC;4EAAG,MAAM,IAAI;4EAAC;4EAAG,MAAM,IAAI;;uEADhC;;;;;;;;;;;;;;;;kEAOjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;sEAAM,KAAK,IAAI;;;;;;;;;;;;+CApBV,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BnC", "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/lib/ai/groq-service.ts"], "sourcesContent": ["import Groq from 'groq-sdk';\nimport {\n  OracleFormsFile,\n  ParsedOracleFile,\n  AIAnalysis,\n  Finding,\n  Suggestion,\n  ChatMessage\n} from '@/types/oracle-forms';\n\nexport class GroqAIService {\n  private groq: Groq;\n\n  constructor(apiKey: string) {\n    this.groq = new Groq({\n      apiKey: apiKey,\n      dangerouslyAllowBrowser: true // Pour utilisation côté client\n    });\n  }\n\n  async analyzeOracleFormsCode(\n    file: OracleFormsFile,\n    parsedFile: ParsedOracleFile,\n    analysisType: 'CODE_REVIEW' | 'DOCUMENTATION' | 'REFACTORING' | 'OPTIMIZATION'\n  ): Promise<AIAnalysis> {\n    const prompt = this.buildAnalysisPrompt(file, parsedFile, analysisType);\n\n    try {\n      const completion = await this.groq.chat.completions.create({\n        messages: [\n          {\n            role: \"system\",\n            content: this.getSystemPrompt(analysisType)\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        model: \"llama-3.3-70b-versatile\",\n        temperature: 0.1,\n        max_tokens: 4000\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      if (!response) {\n        throw new Error('Aucune réponse reçue de l\\'API Groq');\n      }\n\n      return this.parseAnalysisResponse(response, file.id, analysisType);\n    } catch (error) {\n      console.error('Erreur lors de l\\'analyse avec Groq:', error);\n      throw new Error('Échec de l\\'analyse du code Oracle Forms');\n    }\n  }\n\n  async generateDocumentation(\n    file: OracleFormsFile,\n    parsedFile: ParsedOracleFile\n  ): Promise<string> {\n    const prompt = this.buildDocumentationPrompt(file, parsedFile);\n\n    try {\n      const completion = await this.groq.chat.completions.create({\n        messages: [\n          {\n            role: \"system\",\n            content: \"Tu es un expert en Oracle Forms et en documentation technique. Génère une documentation complète et professionnelle en français pour le code Oracle Forms fourni.\"\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        model: \"llama-3.3-70b-versatile\",\n        temperature: 0.2,\n        max_tokens: 6000\n      });\n\n      return completion.choices[0]?.message?.content || 'Erreur lors de la génération de la documentation';\n    } catch (error) {\n      console.error('Erreur lors de la génération de documentation:', error);\n      throw new Error('Échec de la génération de documentation');\n    }\n  }\n\n  async chatWithAssistant(\n    messages: ChatMessage[],\n    context?: { files: OracleFormsFile[], parsedFiles: ParsedOracleFile[] }\n  ): Promise<string> {\n    const systemPrompt = this.getChatSystemPrompt();\n    const contextPrompt = context ? this.buildContextPrompt(context) : '';\n\n    const groqMessages = [\n      { role: \"system\" as const, content: systemPrompt + contextPrompt },\n      ...messages.map(msg => ({\n        role: msg.role as \"user\" | \"assistant\",\n        content: msg.content\n      }))\n    ];\n\n    try {\n      const completion = await this.groq.chat.completions.create({\n        messages: groqMessages,\n        model: \"llama-3.3-70b-versatile\",\n        temperature: 0.3,\n        max_tokens: 3000\n      });\n\n      return completion.choices[0]?.message?.content || 'Désolé, je n\\'ai pas pu traiter votre demande.';\n    } catch (error) {\n      console.error('Erreur lors du chat avec l\\'assistant:', error);\n      throw new Error('Échec de la communication avec l\\'assistant IA');\n    }\n  }\n\n  private getSystemPrompt(analysisType: string): string {\n    const basePrompt = `Tu es un expert senior en Oracle Forms avec plus de 15 ans d'expérience.\nTu maîtrises parfaitement PL/SQL, les meilleures pratiques de développement Oracle Forms,\net les standards de codage bancaires. Tu réponds toujours en français.`;\n\n    switch (analysisType) {\n      case 'CODE_REVIEW':\n        return `${basePrompt}\n\nEffectue une revue de code approfondie en te concentrant sur :\n- La qualité du code et les meilleures pratiques\n- Les erreurs potentielles et les bugs\n- La sécurité et les vulnérabilités\n- La performance et l'optimisation\n- La maintenabilité et la lisibilité\n- La conformité aux standards Oracle Forms\n\nFournis tes résultats au format JSON avec les sections : summary, findings, suggestions.`;\n\n      case 'DOCUMENTATION':\n        return `${basePrompt}\n\nGénère une documentation technique complète incluant :\n- Vue d'ensemble du formulaire/rapport\n- Description des blocs et items\n- Explication des triggers et procédures\n- Flux de données et logique métier\n- Instructions d'utilisation\n- Notes techniques importantes`;\n\n      case 'REFACTORING':\n        return `${basePrompt}\n\nAnalyse le code pour identifier les opportunités de refactorisation :\n- Code dupliqué à factoriser\n- Procédures trop longues à diviser\n- Logique métier à centraliser\n- Amélioration de la structure du code\n- Simplification des expressions complexes`;\n\n      case 'OPTIMIZATION':\n        return `${basePrompt}\n\nIdentifie les optimisations possibles :\n- Performance des requêtes SQL\n- Utilisation efficace de la mémoire\n- Optimisation des triggers\n- Réduction des appels réseau\n- Amélioration de l'expérience utilisateur`;\n\n      default:\n        return basePrompt;\n    }\n  }\n\n  private getChatSystemPrompt(): string {\n    return `Tu es un assistant IA expert en Oracle Forms, spécialisé dans l'aide aux développeurs.\nTu peux :\n- Analyser et expliquer le code Oracle Forms (FMB, RDF, PLL, OLB)\n- Suggérer des améliorations et corrections\n- Répondre aux questions techniques\n- Aider à la résolution de problèmes\n- Fournir des exemples de code\n- Expliquer les meilleures pratiques\n\nTu réponds toujours en français, de manière claire et professionnelle.\nSi tu ne comprends pas une question, demande des clarifications.\nUtilise le contexte des fichiers fournis pour donner des réponses précises.`;\n  }\n\n  private buildAnalysisPrompt(\n    file: OracleFormsFile,\n    parsedFile: ParsedOracleFile,\n    analysisType: string\n  ): string {\n    return `Analyse ce fichier Oracle Forms ${file.type} :\n\n**Nom du fichier :** ${file.name}\n**Type :** ${file.type}\n**Taille :** ${file.size} octets\n\n**Métadonnées :**\n- Version : ${parsedFile.metadata.version}\n- Nom du formulaire : ${parsedFile.metadata.formName || 'N/A'}\n- Description : ${parsedFile.metadata.description || 'N/A'}\n\n**Structure :**\n- Blocs : ${parsedFile.blocks.length}\n- Triggers : ${parsedFile.triggers.length}\n- Procédures : ${parsedFile.procedures.length}\n- Fonctions : ${parsedFile.functions.length}\n- Variables : ${parsedFile.variables.length}\n\n**Contenu du code (extrait) :**\n\\`\\`\\`plsql\n${file.content.substring(0, 2000)}${file.content.length > 2000 ? '...' : ''}\n\\`\\`\\`\n\nType d'analyse demandée : ${analysisType}`;\n  }\n\n  private buildDocumentationPrompt(\n    file: OracleFormsFile,\n    parsedFile: ParsedOracleFile\n  ): string {\n    return `Génère une documentation technique complète pour ce fichier Oracle Forms :\n\n**Fichier :** ${file.name} (${file.type})\n\n**Éléments à documenter :**\n\n**Blocs (${parsedFile.blocks.length}) :**\n${parsedFile.blocks.map(block => `- ${block.name} (${block.type})`).join('\\n')}\n\n**Triggers (${parsedFile.triggers.length}) :**\n${parsedFile.triggers.map(trigger => `- ${trigger.name} (${trigger.level})`).join('\\n')}\n\n**Procédures (${parsedFile.procedures.length}) :**\n${parsedFile.procedures.map(proc => `- ${proc.name}`).join('\\n')}\n\n**Code source :**\n\\`\\`\\`plsql\n${file.content}\n\\`\\`\\``;\n  }\n\n  private buildContextPrompt(context: { files: OracleFormsFile[], parsedFiles: ParsedOracleFile[] }): string {\n    if (!context.files.length) return '';\n\n    return `\\n\\nContexte des fichiers chargés :\n${context.files.map((file, index) => `\n**${file.name}** (${file.type})\n- Blocs : ${context.parsedFiles[index]?.blocks.length || 0}\n- Triggers : ${context.parsedFiles[index]?.triggers.length || 0}\n- Procédures : ${context.parsedFiles[index]?.procedures.length || 0}\n`).join('')}`;\n  }\n\n  private parseAnalysisResponse(response: string, fileId: string, analysisType: string): AIAnalysis {\n    try {\n      // Tentative de parsing JSON\n      const parsed = JSON.parse(response);\n\n      return {\n        id: `analysis_${Date.now()}`,\n        fileId,\n        timestamp: new Date(),\n        type: analysisType as any,\n        summary: parsed.summary || 'Analyse terminée',\n        findings: parsed.findings || [],\n        suggestions: parsed.suggestions || [],\n        score: parsed.score\n      };\n    } catch {\n      // Si le parsing JSON échoue, créer une analyse basique\n      return {\n        id: `analysis_${Date.now()}`,\n        fileId,\n        timestamp: new Date(),\n        type: analysisType as any,\n        summary: response.substring(0, 200) + '...',\n        findings: [],\n        suggestions: [],\n      };\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAUO,MAAM;IACH,KAAW;IAEnB,YAAY,MAAc,CAAE;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,qJAAA,CAAA,UAAI,CAAC;YACnB,QAAQ;YACR,yBAAyB,KAAK,+BAA+B;QAC/D;IACF;IAEA,MAAM,uBACJ,IAAqB,EACrB,UAA4B,EAC5B,YAA8E,EACzD;QACrB,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,MAAM,YAAY;QAE1D,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,eAAe,CAAC;oBAChC;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,OAAO;gBACP,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;YACjD,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,qBAAqB,CAAC,UAAU,KAAK,EAAE,EAAE;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,sBACJ,IAAqB,EACrB,UAA4B,EACX;QACjB,MAAM,SAAS,IAAI,CAAC,wBAAwB,CAAC,MAAM;QAEnD,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,OAAO;gBACP,aAAa;gBACb,YAAY;YACd;YAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QACpD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,kBACJ,QAAuB,EACvB,OAAuE,EACtD;QACjB,MAAM,eAAe,IAAI,CAAC,mBAAmB;QAC7C,MAAM,gBAAgB,UAAU,IAAI,CAAC,kBAAkB,CAAC,WAAW;QAEnE,MAAM,eAAe;YACnB;gBAAE,MAAM;gBAAmB,SAAS,eAAe;YAAc;eAC9D,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;oBACtB,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;gBACtB,CAAC;SACF;QAED,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,UAAU;gBACV,OAAO;gBACP,aAAa;gBACb,YAAY;YACd;YAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QACpD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,gBAAgB,YAAoB,EAAU;QACpD,MAAM,aAAa,CAAC;;sEAE8C,CAAC;QAEnE,OAAQ;YACN,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;;;wFAU2D,CAAC;YAEnF,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;;8BAQC,CAAC;YAEzB,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;0CAOa,CAAC;YAErC,KAAK;gBACH,OAAO,GAAG,WAAW;;;;;;;0CAOa,CAAC;YAErC;gBACE,OAAO;QACX;IACF;IAEQ,sBAA8B;QACpC,OAAO,CAAC;;;;;;;;;;;2EAW+D,CAAC;IAC1E;IAEQ,oBACN,IAAqB,EACrB,UAA4B,EAC5B,YAAoB,EACZ;QACR,OAAO,CAAC,gCAAgC,EAAE,KAAK,IAAI,CAAC;;qBAEnC,EAAE,KAAK,IAAI,CAAC;WACtB,EAAE,KAAK,IAAI,CAAC;aACV,EAAE,KAAK,IAAI,CAAC;;;YAGb,EAAE,WAAW,QAAQ,CAAC,OAAO,CAAC;sBACpB,EAAE,WAAW,QAAQ,CAAC,QAAQ,IAAI,MAAM;gBAC9C,EAAE,WAAW,QAAQ,CAAC,WAAW,IAAI,MAAM;;;UAGjD,EAAE,WAAW,MAAM,CAAC,MAAM,CAAC;aACxB,EAAE,WAAW,QAAQ,CAAC,MAAM,CAAC;eAC3B,EAAE,WAAW,UAAU,CAAC,MAAM,CAAC;cAChC,EAAE,WAAW,SAAS,CAAC,MAAM,CAAC;cAC9B,EAAE,WAAW,SAAS,CAAC,MAAM,CAAC;;;;AAI5C,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,CAAC,MAAM,GAAG,OAAO,QAAQ,GAAG;;;0BAGlD,EAAE,cAAc;IACxC;IAEQ,yBACN,IAAqB,EACrB,UAA4B,EACpB;QACR,OAAO,CAAC;;cAEE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC;;;;SAI/B,EAAE,WAAW,MAAM,CAAC,MAAM,CAAC;AACpC,EAAE,WAAW,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM;;YAEnE,EAAE,WAAW,QAAQ,CAAC,MAAM,CAAC;AACzC,EAAE,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM;;cAE1E,EAAE,WAAW,UAAU,CAAC,MAAM,CAAC;AAC7C,EAAE,WAAW,UAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC,EAAE,EAAE,KAAK,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM;;;;AAIjE,EAAE,KAAK,OAAO,CAAC;MACT,CAAC;IACL;IAEQ,mBAAmB,OAAsE,EAAU;QACzG,IAAI,CAAC,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO;QAElC,OAAO,CAAC;AACZ,EAAE,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;EACpC,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC;UACpB,EAAE,QAAQ,WAAW,CAAC,MAAM,EAAE,OAAO,UAAU,EAAE;aAC9C,EAAE,QAAQ,WAAW,CAAC,MAAM,EAAE,SAAS,UAAU,EAAE;eACjD,EAAE,QAAQ,WAAW,CAAC,MAAM,EAAE,WAAW,UAAU,EAAE;AACpE,CAAC,EAAE,IAAI,CAAC,KAAK;IACX;IAEQ,sBAAsB,QAAgB,EAAE,MAAc,EAAE,YAAoB,EAAc;QAChG,IAAI;YACF,4BAA4B;YAC5B,MAAM,SAAS,KAAK,KAAK,CAAC;YAE1B,OAAO;gBACL,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC5B;gBACA,WAAW,IAAI;gBACf,MAAM;gBACN,SAAS,OAAO,OAAO,IAAI;gBAC3B,UAAU,OAAO,QAAQ,IAAI,EAAE;gBAC/B,aAAa,OAAO,WAAW,IAAI,EAAE;gBACrC,OAAO,OAAO,KAAK;YACrB;QACF,EAAE,OAAM;YACN,uDAAuD;YACvD,OAAO;gBACL,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC5B;gBACA,WAAW,IAAI;gBACf,MAAM;gBACN,SAAS,SAAS,SAAS,CAAC,GAAG,OAAO;gBACtC,UAAU,EAAE;gBACZ,aAAa,EAAE;YACjB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/components/ai-chat.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Send, Bot, User, Loader2, FileText } from 'lucide-react';\nimport { ChatMessage, OracleFormsFile, ParsedOracleFile } from '@/types/oracle-forms';\nimport { GroqAIService } from '@/lib/ai/groq-service';\n\ninterface AIChatProps {\n  files: OracleFormsFile[];\n  parsedFiles: ParsedOracleFile[];\n  groqApiKey: string;\n}\n\nexport function AIChat({ files, parsedFiles, groqApiKey }: AIChatProps) {\n  const [messages, setMessages] = useState<ChatMessage[]>([\n    {\n      id: 'welcome',\n      role: 'assistant',\n      content: 'Bonjour ! Je suis votre assistant IA spécialisé en Oracle Forms. Je peux vous aider à analyser votre code, répondre à vos questions techniques, et suggérer des améliorations. Comment puis-je vous aider aujourd\\'hui ?',\n      timestamp: new Date(),\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    if (!groqApiKey) {\n      alert('Veuillez configurer votre clé API Groq pour utiliser le chat');\n      return;\n    }\n\n    const userMessage: ChatMessage = {\n      id: `user_${Date.now()}`,\n      role: 'user',\n      content: inputMessage.trim(),\n      timestamp: new Date(),\n      fileContext: files.map(f => f.name)\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      const aiService = new GroqAIService(groqApiKey);\n      const response = await aiService.chatWithAssistant(\n        [...messages, userMessage],\n        { files, parsedFiles }\n      );\n\n      const assistantMessage: ChatMessage = {\n        id: `assistant_${Date.now()}`,\n        role: 'assistant',\n        content: response,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n    } catch (error) {\n      console.error('Erreur lors du chat:', error);\n      const errorMessage: ChatMessage = {\n        id: `error_${Date.now()}`,\n        role: 'assistant',\n        content: 'Désolé, une erreur s\\'est produite lors du traitement de votre message. Veuillez réessayer.',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatMessage = (content: string) => {\n    // Simple formatage pour les blocs de code\n    const parts = content.split('```');\n    return parts.map((part, index) => {\n      if (index % 2 === 1) {\n        // C'est un bloc de code\n        return (\n          <pre key={index} className=\"bg-gray-100 p-3 rounded-lg my-2 overflow-x-auto\">\n            <code className=\"text-sm\">{part}</code>\n          </pre>\n        );\n      } else {\n        // C'est du texte normal\n        return (\n          <div key={index} className=\"whitespace-pre-wrap\">\n            {part}\n          </div>\n        );\n      }\n    });\n  };\n\n  const suggestedQuestions = [\n    \"Peux-tu analyser la structure de mon formulaire ?\",\n    \"Quelles sont les meilleures pratiques pour les triggers Oracle Forms ?\",\n    \"Comment optimiser les performances de mes requêtes ?\",\n    \"Peux-tu identifier les problèmes potentiels dans mon code ?\",\n    \"Comment améliorer la sécurité de mon application ?\"\n  ];\n\n  return (\n    <div className=\"h-full flex flex-col bg-white\">\n      {/* En-tête */}\n      <div className=\"border-b p-4 bg-gray-50\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <Bot className=\"h-8 w-8 text-blue-600\" />\n            <div>\n              <h2 className=\"text-lg font-semibold\">Assistant IA Oracle Forms</h2>\n              <p className=\"text-sm text-gray-600\">\n                {files.length > 0 \n                  ? `${files.length} fichier(s) chargé(s) • Prêt à vous aider`\n                  : 'Chargez des fichiers pour une assistance contextuelle'\n                }\n              </p>\n            </div>\n          </div>\n          \n          {files.length > 0 && (\n            <div className=\"flex items-center space-x-2\">\n              <FileText className=\"h-4 w-4 text-gray-400\" />\n              <span className=\"text-sm text-gray-600\">\n                Contexte: {files.map(f => f.name).join(', ')}\n              </span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n          >\n            <div\n              className={`max-w-3xl flex space-x-3 ${\n                message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''\n              }`}\n            >\n              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\n                message.role === 'user' \n                  ? 'bg-blue-600 text-white' \n                  : 'bg-gray-200 text-gray-600'\n              }`}>\n                {message.role === 'user' ? (\n                  <User className=\"h-4 w-4\" />\n                ) : (\n                  <Bot className=\"h-4 w-4\" />\n                )}\n              </div>\n              \n              <div className={`rounded-lg p-4 ${\n                message.role === 'user'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-100 text-gray-900'\n              }`}>\n                <div className=\"text-sm\">\n                  {formatMessage(message.content)}\n                </div>\n                <div className={`text-xs mt-2 ${\n                  message.role === 'user' ? 'text-blue-100' : 'text-gray-500'\n                }`}>\n                  {message.timestamp.toLocaleTimeString()}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n        \n        {isLoading && (\n          <div className=\"flex justify-start\">\n            <div className=\"max-w-3xl flex space-x-3\">\n              <div className=\"flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center\">\n                <Bot className=\"h-4 w-4\" />\n              </div>\n              <div className=\"bg-gray-100 rounded-lg p-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  <span className=\"text-sm text-gray-600\">L'assistant réfléchit...</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Questions suggérées (affichées seulement s'il n'y a que le message de bienvenue) */}\n      {messages.length === 1 && (\n        <div className=\"border-t p-4 bg-gray-50\">\n          <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Questions suggérées :</h3>\n          <div className=\"flex flex-wrap gap-2\">\n            {suggestedQuestions.map((question, index) => (\n              <button\n                key={index}\n                onClick={() => setInputMessage(question)}\n                className=\"px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                {question}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Zone de saisie */}\n      <div className=\"border-t p-4 bg-white\">\n        <div className=\"flex space-x-3\">\n          <div className=\"flex-1\">\n            <textarea\n              ref={textareaRef}\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder={\n                groqApiKey \n                  ? \"Posez votre question sur Oracle Forms...\" \n                  : \"Configurez votre clé API Groq pour utiliser le chat\"\n              }\n              disabled={!groqApiKey || isLoading}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed\"\n              rows={3}\n            />\n          </div>\n          <button\n            onClick={handleSendMessage}\n            disabled={!inputMessage.trim() || !groqApiKey || isLoading}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\"\n          >\n            {isLoading ? (\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\n            ) : (\n              <Send className=\"h-4 w-4\" />\n            )}\n            <span>Envoyer</span>\n          </button>\n        </div>\n        \n        <div className=\"mt-2 text-xs text-gray-500\">\n          Appuyez sur Entrée pour envoyer, Shift+Entrée pour une nouvelle ligne\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAaO,SAAS,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAe;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,IAAI,CAAC,YAAY;YACf,MAAM;YACN;QACF;QAEA,MAAM,cAA2B;YAC/B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,MAAM;YACN,SAAS,aAAa,IAAI;YAC1B,WAAW,IAAI;YACf,aAAa,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACpC;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,YAAY,IAAI,mIAAA,CAAA,gBAAa,CAAC;YACpC,MAAM,WAAW,MAAM,UAAU,iBAAiB,CAChD;mBAAI;gBAAU;aAAY,EAC1B;gBAAE;gBAAO;YAAY;YAGvB,MAAM,mBAAgC;gBACpC,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,eAA4B;gBAChC,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;gBACzB,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,0CAA0C;QAC1C,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;YACtB,IAAI,QAAQ,MAAM,GAAG;gBACnB,wBAAwB;gBACxB,qBACE,8OAAC;oBAAgB,WAAU;8BACzB,cAAA,8OAAC;wBAAK,WAAU;kCAAW;;;;;;mBADnB;;;;;YAId,OAAO;gBACL,wBAAwB;gBACxB,qBACE,8OAAC;oBAAgB,WAAU;8BACxB;mBADO;;;;;YAId;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAE,WAAU;sDACV,MAAM,MAAM,GAAG,IACZ,GAAG,MAAM,MAAM,CAAC,yCAAyC,CAAC,GAC1D;;;;;;;;;;;;;;;;;;wBAMT,MAAM,MAAM,GAAG,mBACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;;wCAAwB;wCAC3B,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC;gBAAI,WAAU;;oBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sCAE9E,cAAA,8OAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,QAAQ,IAAI,KAAK,SAAS,qCAAqC,IAC/D;;kDAEF,8OAAC;wCAAI,WAAW,CAAC,oEAAoE,EACnF,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;kDACC,QAAQ,IAAI,KAAK,uBAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;iEAEhB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAInB,8OAAC;wCAAI,WAAW,CAAC,eAAe,EAC9B,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;;0DACA,8OAAC;gDAAI,WAAU;0DACZ,cAAc,QAAQ,OAAO;;;;;;0DAEhC,8OAAC;gDAAI,WAAW,CAAC,aAAa,EAC5B,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;0DACC,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;2BA/BtC,QAAQ,EAAE;;;;;oBAsClB,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,8OAAC;wBAAI,KAAK;;;;;;;;;;;;YAIX,SAAS,MAAM,KAAK,mBACnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CAET;+BAJI;;;;;;;;;;;;;;;;0BAYf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,YAAY;oCACZ,aACE,aACI,6CACA;oCAEN,UAAU,CAAC,cAAc;oCACzB,WAAU;oCACV,MAAM;;;;;;;;;;;0CAGV,8OAAC;gCACC,SAAS;gCACT,UAAU,CAAC,aAAa,IAAI,MAAM,CAAC,cAAc;gCACjD,WAAU;;oCAET,0BACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAElB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAIV,8OAAC;wBAAI,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;AAMpD", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/components/analysis-panel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Info, Lightbulb, Clock, FileText } from 'lucide-react';\nimport { AIAnalysis, OracleFormsFile, Finding, Suggestion } from '@/types/oracle-forms';\n\ninterface AnalysisPanelProps {\n  analyses: AIAnalysis[];\n  files: OracleFormsFile[];\n}\n\nexport function AnalysisPanel({ analyses, files }: AnalysisPanelProps) {\n  const [selectedAnalysisId, setSelectedAnalysisId] = useState<string | null>(null);\n  const [filterType, setFilterType] = useState<string>('all');\n\n  const selectedAnalysis = analyses.find(a => a.id === selectedAnalysisId);\n\n  const getAnalysisTypeLabel = (type: string) => {\n    switch (type) {\n      case 'CODE_REVIEW':\n        return 'Revue de Code';\n      case 'DOCUMENTATION':\n        return 'Documentation';\n      case 'REFACTORING':\n        return 'Refactorisation';\n      case 'OPTIMIZATION':\n        return 'Optimisation';\n      default:\n        return type;\n    }\n  };\n\n  const getAnalysisTypeColor = (type: string) => {\n    switch (type) {\n      case 'CODE_REVIEW':\n        return 'bg-blue-100 text-blue-800';\n      case 'DOCUMENTATION':\n        return 'bg-green-100 text-green-800';\n      case 'REFACTORING':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'OPTIMIZATION':\n        return 'bg-purple-100 text-purple-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getSeverityIcon = (severity: string) => {\n    switch (severity) {\n      case 'HIGH':\n        return <AlertTriangle className=\"h-4 w-4 text-red-500\" />;\n      case 'MEDIUM':\n        return <Info className=\"h-4 w-4 text-yellow-500\" />;\n      case 'LOW':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      default:\n        return <Info className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getSeverityColor = (severity: string) => {\n    switch (severity) {\n      case 'HIGH':\n        return 'border-red-200 bg-red-50';\n      case 'MEDIUM':\n        return 'border-yellow-200 bg-yellow-50';\n      case 'LOW':\n        return 'border-green-200 bg-green-50';\n      default:\n        return 'border-gray-200 bg-gray-50';\n    }\n  };\n\n  const getFileName = (fileId: string) => {\n    const file = files.find(f => f.id === fileId);\n    return file ? file.name : 'Fichier inconnu';\n  };\n\n  const filteredAnalyses = analyses.filter(analysis => {\n    if (filterType === 'all') return true;\n    return analysis.type === filterType;\n  });\n\n  if (analyses.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center h-full text-gray-500\">\n        <div className=\"text-center\">\n          <FileText className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n          <p className=\"text-lg font-medium mb-2\">Aucune analyse disponible</p>\n          <p className=\"text-sm\">\n            Sélectionnez un fichier et lancez une analyse pour voir les résultats ici.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full flex\">\n      {/* Liste des analyses */}\n      <div className=\"w-1/3 border-r bg-gray-50 overflow-auto\">\n        <div className=\"p-4 border-b bg-white\">\n          <h2 className=\"text-lg font-semibold mb-3\">Analyses ({analyses.length})</h2>\n          \n          {/* Filtres */}\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm\"\n          >\n            <option value=\"all\">Tous les types</option>\n            <option value=\"CODE_REVIEW\">Revue de Code</option>\n            <option value=\"DOCUMENTATION\">Documentation</option>\n            <option value=\"REFACTORING\">Refactorisation</option>\n            <option value=\"OPTIMIZATION\">Optimisation</option>\n          </select>\n        </div>\n\n        <div className=\"p-4 space-y-3\">\n          {filteredAnalyses.map((analysis) => (\n            <div\n              key={analysis.id}\n              className={`p-3 rounded-lg border cursor-pointer transition-colors ${\n                selectedAnalysisId === analysis.id\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 bg-white hover:border-gray-300'\n              }`}\n              onClick={() => setSelectedAnalysisId(analysis.id)}\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className={`px-2 py-1 text-xs font-medium rounded ${getAnalysisTypeColor(analysis.type)}`}>\n                  {getAnalysisTypeLabel(analysis.type)}\n                </span>\n                <div className=\"flex items-center text-xs text-gray-500\">\n                  <Clock className=\"h-3 w-3 mr-1\" />\n                  {analysis.timestamp.toLocaleDateString()}\n                </div>\n              </div>\n              \n              <h3 className=\"font-medium text-sm mb-1\">{getFileName(analysis.fileId)}</h3>\n              <p className=\"text-xs text-gray-600 line-clamp-2\">{analysis.summary}</p>\n              \n              <div className=\"flex items-center justify-between mt-2 text-xs text-gray-500\">\n                <span>{analysis.findings.length} problèmes</span>\n                <span>{analysis.suggestions.length} suggestions</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Détails de l'analyse sélectionnée */}\n      <div className=\"flex-1 overflow-auto\">\n        {selectedAnalysis ? (\n          <div className=\"p-6\">\n            <div className=\"mb-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-xl font-semibold\">\n                  {getAnalysisTypeLabel(selectedAnalysis.type)}\n                </h2>\n                <span className={`px-3 py-1 text-sm font-medium rounded ${getAnalysisTypeColor(selectedAnalysis.type)}`}>\n                  {getFileName(selectedAnalysis.fileId)}\n                </span>\n              </div>\n              \n              <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n                <h3 className=\"font-medium mb-2\">Résumé</h3>\n                <p className=\"text-gray-700\">{selectedAnalysis.summary}</p>\n              </div>\n\n              {selectedAnalysis.score && (\n                <div className=\"mb-6\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"font-medium\">Score de qualité</span>\n                    <span className=\"text-lg font-bold\">{selectedAnalysis.score}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full ${\n                        selectedAnalysis.score >= 80\n                          ? 'bg-green-500'\n                          : selectedAnalysis.score >= 60\n                          ? 'bg-yellow-500'\n                          : 'bg-red-500'\n                      }`}\n                      style={{ width: `${selectedAnalysis.score}%` }}\n                    ></div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Problèmes identifiés */}\n            {selectedAnalysis.findings.length > 0 && (\n              <div className=\"mb-8\">\n                <h3 className=\"text-lg font-semibold mb-4\">\n                  Problèmes identifiés ({selectedAnalysis.findings.length})\n                </h3>\n                <div className=\"space-y-4\">\n                  {selectedAnalysis.findings.map((finding) => (\n                    <div\n                      key={finding.id}\n                      className={`border rounded-lg p-4 ${getSeverityColor(finding.severity)}`}\n                    >\n                      <div className=\"flex items-start space-x-3\">\n                        {getSeverityIcon(finding.severity)}\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <h4 className=\"font-medium\">{finding.title}</h4>\n                            <span className={`px-2 py-1 text-xs font-medium rounded ${\n                              finding.type === 'ERROR' ? 'bg-red-100 text-red-800' :\n                              finding.type === 'WARNING' ? 'bg-yellow-100 text-yellow-800' :\n                              'bg-blue-100 text-blue-800'\n                            }`}>\n                              {finding.type}\n                            </span>\n                          </div>\n                          <p className=\"text-gray-700 mb-2\">{finding.description}</p>\n                          <div className=\"text-sm text-gray-600\">\n                            Ligne {finding.location.startLine}\n                            {finding.location.endLine !== finding.location.startLine && \n                              ` - ${finding.location.endLine}`\n                            }\n                          </div>\n                          {finding.code && (\n                            <pre className=\"mt-2 bg-white p-2 rounded text-sm overflow-x-auto\">\n                              <code>{finding.code}</code>\n                            </pre>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Suggestions d'amélioration */}\n            {selectedAnalysis.suggestions.length > 0 && (\n              <div>\n                <h3 className=\"text-lg font-semibold mb-4\">\n                  Suggestions d'amélioration ({selectedAnalysis.suggestions.length})\n                </h3>\n                <div className=\"space-y-4\">\n                  {selectedAnalysis.suggestions.map((suggestion) => (\n                    <div key={suggestion.id} className=\"border rounded-lg p-4 bg-white\">\n                      <div className=\"flex items-start space-x-3\">\n                        <Lightbulb className=\"h-5 w-5 text-yellow-500 mt-0.5\" />\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <h4 className=\"font-medium\">{suggestion.title}</h4>\n                            <div className=\"flex items-center space-x-2\">\n                              <span className={`px-2 py-1 text-xs font-medium rounded ${\n                                suggestion.impact === 'HIGH' ? 'bg-red-100 text-red-800' :\n                                suggestion.impact === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :\n                                'bg-green-100 text-green-800'\n                              }`}>\n                                Impact {suggestion.impact}\n                              </span>\n                              <button\n                                className={`px-3 py-1 text-xs font-medium rounded ${\n                                  suggestion.approved\n                                    ? 'bg-green-100 text-green-800'\n                                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'\n                                }`}\n                              >\n                                {suggestion.approved ? 'Approuvé' : 'Approuver'}\n                              </button>\n                            </div>\n                          </div>\n                          <p className=\"text-gray-700 mb-3\">{suggestion.description}</p>\n                          \n                          <div className=\"grid grid-cols-2 gap-4\">\n                            <div>\n                              <h5 className=\"font-medium text-sm mb-2\">Code actuel:</h5>\n                              <pre className=\"bg-red-50 p-2 rounded text-sm overflow-x-auto\">\n                                <code>{suggestion.originalCode}</code>\n                              </pre>\n                            </div>\n                            <div>\n                              <h5 className=\"font-medium text-sm mb-2\">Code suggéré:</h5>\n                              <pre className=\"bg-green-50 p-2 rounded text-sm overflow-x-auto\">\n                                <code>{suggestion.suggestedCode}</code>\n                              </pre>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"flex items-center justify-center h-full text-gray-500\">\n            <p>Sélectionnez une analyse pour voir les détails</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWO,SAAS,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAsB;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,mBAAmB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAErD,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtC,OAAO,OAAO,KAAK,IAAI,GAAG;IAC5B;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,IAAI,eAAe,OAAO,OAAO;QACjC,OAAO,SAAS,IAAI,KAAK;IAC3B;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAE,WAAU;kCAA2B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAM/B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA6B;oCAAW,SAAS,MAAM;oCAAC;;;;;;;0CAGtE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,8OAAC;wCAAO,OAAM;kDAAgB;;;;;;kDAC9B,8OAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,8OAAC;wCAAO,OAAM;kDAAe;;;;;;;;;;;;;;;;;;kCAIjC,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;gCAEC,WAAW,CAAC,uDAAuD,EACjE,uBAAuB,SAAS,EAAE,GAC9B,+BACA,kDACJ;gCACF,SAAS,IAAM,sBAAsB,SAAS,EAAE;;kDAEhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,sCAAsC,EAAE,qBAAqB,SAAS,IAAI,GAAG;0DAC5F,qBAAqB,SAAS,IAAI;;;;;;0DAErC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,SAAS,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;kDAI1C,8OAAC;wCAAG,WAAU;kDAA4B,YAAY,SAAS,MAAM;;;;;;kDACrE,8OAAC;wCAAE,WAAU;kDAAsC,SAAS,OAAO;;;;;;kDAEnE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAM,SAAS,QAAQ,CAAC,MAAM;oDAAC;;;;;;;0DAChC,8OAAC;;oDAAM,SAAS,WAAW,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;+BAvBhC,SAAS,EAAE;;;;;;;;;;;;;;;;0BA+BxB,8OAAC;gBAAI,WAAU;0BACZ,iCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,qBAAqB,iBAAiB,IAAI;;;;;;sDAE7C,8OAAC;4CAAK,WAAW,CAAC,sCAAsC,EAAE,qBAAqB,iBAAiB,IAAI,GAAG;sDACpG,YAAY,iBAAiB,MAAM;;;;;;;;;;;;8CAIxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAE,WAAU;sDAAiB,iBAAiB,OAAO;;;;;;;;;;;;gCAGvD,iBAAiB,KAAK,kBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;;wDAAqB,iBAAiB,KAAK;wDAAC;;;;;;;;;;;;;sDAE9D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAW,CAAC,iBAAiB,EAC3B,iBAAiB,KAAK,IAAI,KACtB,iBACA,iBAAiB,KAAK,IAAI,KAC1B,kBACA,cACJ;gDACF,OAAO;oDAAE,OAAO,GAAG,iBAAiB,KAAK,CAAC,CAAC,CAAC;gDAAC;;;;;;;;;;;;;;;;;;;;;;;wBAQtD,iBAAiB,QAAQ,CAAC,MAAM,GAAG,mBAClC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA6B;wCAClB,iBAAiB,QAAQ,CAAC,MAAM;wCAAC;;;;;;;8CAE1D,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC9B,8OAAC;4CAEC,WAAW,CAAC,sBAAsB,EAAE,iBAAiB,QAAQ,QAAQ,GAAG;sDAExE,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,gBAAgB,QAAQ,QAAQ;kEACjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAe,QAAQ,KAAK;;;;;;kFAC1C,8OAAC;wEAAK,WAAW,CAAC,sCAAsC,EACtD,QAAQ,IAAI,KAAK,UAAU,4BAC3B,QAAQ,IAAI,KAAK,YAAY,kCAC7B,6BACA;kFACC,QAAQ,IAAI;;;;;;;;;;;;0EAGjB,8OAAC;gEAAE,WAAU;0EAAsB,QAAQ,WAAW;;;;;;0EACtD,8OAAC;gEAAI,WAAU;;oEAAwB;oEAC9B,QAAQ,QAAQ,CAAC,SAAS;oEAChC,QAAQ,QAAQ,CAAC,OAAO,KAAK,QAAQ,QAAQ,CAAC,SAAS,IACtD,CAAC,GAAG,EAAE,QAAQ,QAAQ,CAAC,OAAO,EAAE;;;;;;;4DAGnC,QAAQ,IAAI,kBACX,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;8EAAM,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;2CAzBtB,QAAQ,EAAE;;;;;;;;;;;;;;;;wBAqCxB,iBAAiB,WAAW,CAAC,MAAM,GAAG,mBACrC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAA6B;wCACZ,iBAAiB,WAAW,CAAC,MAAM;wCAAC;;;;;;;8CAEnE,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,WAAW,CAAC,GAAG,CAAC,CAAC,2BACjC,8OAAC;4CAAwB,WAAU;sDACjC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAe,WAAW,KAAK;;;;;;kFAC7C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAW,CAAC,sCAAsC,EACtD,WAAW,MAAM,KAAK,SAAS,4BAC/B,WAAW,MAAM,KAAK,WAAW,kCACjC,+BACA;;oFAAE;oFACM,WAAW,MAAM;;;;;;;0FAE3B,8OAAC;gFACC,WAAW,CAAC,sCAAsC,EAChD,WAAW,QAAQ,GACf,gCACA,+CACJ;0FAED,WAAW,QAAQ,GAAG,aAAa;;;;;;;;;;;;;;;;;;0EAI1C,8OAAC;gEAAE,WAAU;0EAAsB,WAAW,WAAW;;;;;;0EAEzD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAA2B;;;;;;0FACzC,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;8FAAM,WAAW,YAAY;;;;;;;;;;;;;;;;;kFAGlC,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAA2B;;;;;;0FACzC,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;8FAAM,WAAW,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CArCjC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;yCAkDjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/lib/parsers/base-parser.ts"], "sourcesContent": ["import { \n  OracleFormsFile, \n  ParsedOracleFile, \n  FileMetadata, \n  Block, \n  Trigger, \n  Procedure, \n  Function, \n  Variable, \n  Dependency \n} from '@/types/oracle-forms';\n\nexport abstract class BaseOracleParser {\n  protected file: OracleFormsFile;\n\n  constructor(file: OracleFormsFile) {\n    this.file = file;\n  }\n\n  abstract parse(): Promise<ParsedOracleFile>;\n\n  protected extractMetadata(): FileMetadata {\n    // Implémentation de base pour extraire les métadonnées\n    return {\n      version: this.extractVersion(),\n      formName: this.extractFormName(),\n      description: this.extractDescription(),\n      author: this.extractAuthor(),\n      creationDate: this.extractCreationDate(),\n      lastModified: this.file.lastModified\n    };\n  }\n\n  protected extractVersion(): string {\n    // Recherche de la version dans le contenu du fichier\n    const versionMatch = this.file.content.match(/VERSION\\s*=\\s*['\"]([^'\"]+)['\"]/i);\n    return versionMatch ? versionMatch[1] : 'Unknown';\n  }\n\n  protected extractFormName(): string | undefined {\n    // Recherche du nom du formulaire\n    const formNameMatch = this.file.content.match(/FORM_NAME\\s*=\\s*['\"]([^'\"]+)['\"]/i);\n    return formNameMatch ? formNameMatch[1] : undefined;\n  }\n\n  protected extractDescription(): string | undefined {\n    // Recherche de la description\n    const descMatch = this.file.content.match(/DESCRIPTION\\s*=\\s*['\"]([^'\"]+)['\"]/i);\n    return descMatch ? descMatch[1] : undefined;\n  }\n\n  protected extractAuthor(): string | undefined {\n    // Recherche de l'auteur\n    const authorMatch = this.file.content.match(/AUTHOR\\s*=\\s*['\"]([^'\"]+)['\"]/i);\n    return authorMatch ? authorMatch[1] : undefined;\n  }\n\n  protected extractCreationDate(): Date | undefined {\n    // Recherche de la date de création\n    const dateMatch = this.file.content.match(/CREATION_DATE\\s*=\\s*['\"]([^'\"]+)['\"]/i);\n    return dateMatch ? new Date(dateMatch[1]) : undefined;\n  }\n\n  protected extractTriggers(content: string, level: 'FORM' | 'BLOCK' | 'ITEM', parentId?: string): Trigger[] {\n    const triggers: Trigger[] = [];\n    \n    // Pattern pour identifier les triggers\n    const triggerPattern = /TRIGGER\\s+(\\w+)\\s*\\n([\\s\\S]*?)END\\s+TRIGGER/gi;\n    let match;\n\n    while ((match = triggerPattern.exec(content)) !== null) {\n      const triggerName = match[1];\n      const triggerCode = match[2].trim();\n      \n      triggers.push({\n        id: `${parentId || 'form'}_trigger_${triggerName}_${Date.now()}`,\n        name: triggerName,\n        type: triggerName,\n        code: triggerCode,\n        level,\n        parentId,\n        lineNumber: this.getLineNumber(content, match.index)\n      });\n    }\n\n    return triggers;\n  }\n\n  protected extractProcedures(content: string): Procedure[] {\n    const procedures: Procedure[] = [];\n    \n    // Pattern pour identifier les procédures\n    const procPattern = /PROCEDURE\\s+(\\w+)\\s*\\((.*?)\\)\\s*IS\\s*([\\s\\S]*?)END\\s+\\1/gi;\n    let match;\n\n    while ((match = procPattern.exec(content)) !== null) {\n      const procName = match[1];\n      const paramString = match[2];\n      const procCode = match[3].trim();\n      \n      procedures.push({\n        id: `proc_${procName}_${Date.now()}`,\n        name: procName,\n        parameters: this.parseParameters(paramString),\n        code: procCode,\n        lineNumber: this.getLineNumber(content, match.index)\n      });\n    }\n\n    return procedures;\n  }\n\n  protected extractFunctions(content: string): Function[] {\n    const functions: Function[] = [];\n    \n    // Pattern pour identifier les fonctions\n    const funcPattern = /FUNCTION\\s+(\\w+)\\s*\\((.*?)\\)\\s*RETURN\\s+(\\w+)\\s*IS\\s*([\\s\\S]*?)END\\s+\\1/gi;\n    let match;\n\n    while ((match = funcPattern.exec(content)) !== null) {\n      const funcName = match[1];\n      const paramString = match[2];\n      const returnType = match[3];\n      const funcCode = match[4].trim();\n      \n      functions.push({\n        id: `func_${funcName}_${Date.now()}`,\n        name: funcName,\n        parameters: this.parseParameters(paramString),\n        code: funcCode,\n        returnType,\n        lineNumber: this.getLineNumber(content, match.index)\n      });\n    }\n\n    return functions;\n  }\n\n  protected extractVariables(content: string): Variable[] {\n    const variables: Variable[] = [];\n    \n    // Pattern pour identifier les variables\n    const varPattern = /(\\w+)\\s+(\\w+(?:\\(\\d+\\))?)\\s*(?::=\\s*([^;]+))?;/gi;\n    let match;\n\n    while ((match = varPattern.exec(content)) !== null) {\n      const varName = match[1];\n      const varType = match[2];\n      const defaultValue = match[3]?.trim();\n      \n      variables.push({\n        id: `var_${varName}_${Date.now()}`,\n        name: varName,\n        type: varType,\n        scope: 'LOCAL', // Par défaut, peut être ajusté selon le contexte\n        defaultValue,\n        lineNumber: this.getLineNumber(content, match.index)\n      });\n    }\n\n    return variables;\n  }\n\n  protected parseParameters(paramString: string) {\n    if (!paramString.trim()) return [];\n    \n    return paramString.split(',').map(param => {\n      const parts = param.trim().split(/\\s+/);\n      const name = parts[0];\n      const mode = parts.includes('OUT') ? (parts.includes('IN') ? 'IN OUT' : 'OUT') : 'IN';\n      const type = parts[parts.length - 1];\n      \n      return {\n        name,\n        type,\n        mode: mode as 'IN' | 'OUT' | 'IN OUT'\n      };\n    });\n  }\n\n  protected getLineNumber(content: string, index: number): number {\n    return content.substring(0, index).split('\\n').length;\n  }\n\n  protected extractDependencies(content: string): Dependency[] {\n    const dependencies: Dependency[] = [];\n    \n    // Pattern pour identifier les dépendances (INCLUDE, LIBRARY, etc.)\n    const depPatterns = [\n      /INCLUDE\\s+['\"]([^'\"]+)['\"]/gi,\n      /LIBRARY\\s+['\"]([^'\"]+)['\"]/gi,\n      /CALL_FORM\\s*\\(\\s*['\"]([^'\"]+)['\"]/gi\n    ];\n\n    depPatterns.forEach(pattern => {\n      let match;\n      while ((match = pattern.exec(content)) !== null) {\n        dependencies.push({\n          id: `dep_${match[1]}_${Date.now()}`,\n          name: match[1],\n          type: 'LIBRARY',\n          required: true\n        });\n      }\n    });\n\n    return dependencies;\n  }\n}\n"], "names": [], "mappings": ";;;AAYO,MAAe;IACV,KAAsB;IAEhC,YAAY,IAAqB,CAAE;QACjC,IAAI,CAAC,IAAI,GAAG;IACd;IAIU,kBAAgC;QACxC,uDAAuD;QACvD,OAAO;YACL,SAAS,IAAI,CAAC,cAAc;YAC5B,UAAU,IAAI,CAAC,eAAe;YAC9B,aAAa,IAAI,CAAC,kBAAkB;YACpC,QAAQ,IAAI,CAAC,aAAa;YAC1B,cAAc,IAAI,CAAC,mBAAmB;YACtC,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY;QACtC;IACF;IAEU,iBAAyB;QACjC,qDAAqD;QACrD,MAAM,eAAe,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC7C,OAAO,eAAe,YAAY,CAAC,EAAE,GAAG;IAC1C;IAEU,kBAAsC;QAC9C,iCAAiC;QACjC,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC9C,OAAO,gBAAgB,aAAa,CAAC,EAAE,GAAG;IAC5C;IAEU,qBAAyC;QACjD,8BAA8B;QAC9B,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC1C,OAAO,YAAY,SAAS,CAAC,EAAE,GAAG;IACpC;IAEU,gBAAoC;QAC5C,wBAAwB;QACxB,MAAM,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC5C,OAAO,cAAc,WAAW,CAAC,EAAE,GAAG;IACxC;IAEU,sBAAwC;QAChD,mCAAmC;QACnC,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC1C,OAAO,YAAY,IAAI,KAAK,SAAS,CAAC,EAAE,IAAI;IAC9C;IAEU,gBAAgB,OAAe,EAAE,KAAgC,EAAE,QAAiB,EAAa;QACzG,MAAM,WAAsB,EAAE;QAE9B,uCAAuC;QACvC,MAAM,iBAAiB;QACvB,IAAI;QAEJ,MAAO,CAAC,QAAQ,eAAe,IAAI,CAAC,QAAQ,MAAM,KAAM;YACtD,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI;YAEjC,SAAS,IAAI,CAAC;gBACZ,IAAI,GAAG,YAAY,OAAO,SAAS,EAAE,YAAY,CAAC,EAAE,KAAK,GAAG,IAAI;gBAChE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN;gBACA;gBACA,YAAY,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM,KAAK;YACrD;QACF;QAEA,OAAO;IACT;IAEU,kBAAkB,OAAe,EAAe;QACxD,MAAM,aAA0B,EAAE;QAElC,yCAAyC;QACzC,MAAM,cAAc;QACpB,IAAI;QAEJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,IAAI;YAE9B,WAAW,IAAI,CAAC;gBACd,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI;gBACpC,MAAM;gBACN,YAAY,IAAI,CAAC,eAAe,CAAC;gBACjC,MAAM;gBACN,YAAY,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM,KAAK;YACrD;QACF;QAEA,OAAO;IACT;IAEU,iBAAiB,OAAe,EAAc;QACtD,MAAM,YAAwB,EAAE;QAEhC,wCAAwC;QACxC,MAAM,cAAc;QACpB,IAAI;QAEJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,MAAM,cAAc,KAAK,CAAC,EAAE;YAC5B,MAAM,aAAa,KAAK,CAAC,EAAE;YAC3B,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,IAAI;YAE9B,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI;gBACpC,MAAM;gBACN,YAAY,IAAI,CAAC,eAAe,CAAC;gBACjC,MAAM;gBACN;gBACA,YAAY,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM,KAAK;YACrD;QACF;QAEA,OAAO;IACT;IAEU,iBAAiB,OAAe,EAAc;QACtD,MAAM,YAAwB,EAAE;QAEhC,wCAAwC;QACxC,MAAM,aAAa;QACnB,IAAI;QAEJ,MAAO,CAAC,QAAQ,WAAW,IAAI,CAAC,QAAQ,MAAM,KAAM;YAClD,MAAM,UAAU,KAAK,CAAC,EAAE;YACxB,MAAM,UAAU,KAAK,CAAC,EAAE;YACxB,MAAM,eAAe,KAAK,CAAC,EAAE,EAAE;YAE/B,UAAU,IAAI,CAAC;gBACb,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,KAAK,GAAG,IAAI;gBAClC,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP;gBACA,YAAY,IAAI,CAAC,aAAa,CAAC,SAAS,MAAM,KAAK;YACrD;QACF;QAEA,OAAO;IACT;IAEU,gBAAgB,WAAmB,EAAE;QAC7C,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO,EAAE;QAElC,OAAO,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA;YAChC,MAAM,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC;YACjC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,OAAO,MAAM,QAAQ,CAAC,SAAU,MAAM,QAAQ,CAAC,QAAQ,WAAW,QAAS;YACjF,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;YAEpC,OAAO;gBACL;gBACA;gBACA,MAAM;YACR;QACF;IACF;IAEU,cAAc,OAAe,EAAE,KAAa,EAAU;QAC9D,OAAO,QAAQ,SAAS,CAAC,GAAG,OAAO,KAAK,CAAC,MAAM,MAAM;IACvD;IAEU,oBAAoB,OAAe,EAAgB;QAC3D,MAAM,eAA6B,EAAE;QAErC,mEAAmE;QACnE,MAAM,cAAc;YAClB;YACA;YACA;SACD;QAED,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI;YACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;gBAC/C,aAAa,IAAI,CAAC;oBAChB,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;oBACnC,MAAM,KAAK,CAAC,EAAE;oBACd,MAAM;oBACN,UAAU;gBACZ;YACF;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/lib/parsers/fmb-parser.ts"], "sourcesContent": ["import { BaseOracleParser } from './base-parser';\nimport { ParsedOracleFile, Block, Item } from '@/types/oracle-forms';\n\nexport class FMBParser extends BaseOracleParser {\n  async parse(): Promise<ParsedOracleFile> {\n    const metadata = this.extractMetadata();\n    const blocks = this.extractBlocks();\n    const triggers = this.extractTriggers(this.file.content, 'FORM');\n    const procedures = this.extractProcedures(this.file.content);\n    const functions = this.extractFunctions(this.file.content);\n    const variables = this.extractVariables(this.file.content);\n    const dependencies = this.extractDependencies(this.file.content);\n\n    return {\n      metadata,\n      blocks,\n      triggers,\n      procedures,\n      functions,\n      variables,\n      dependencies\n    };\n  }\n\n  private extractBlocks(): Block[] {\n    const blocks: Block[] = [];\n    \n    // Pattern pour identifier les blocs dans un fichier FMB\n    const blockPattern = /BLOCK\\s+(\\w+)\\s*\\{([\\s\\S]*?)\\}/gi;\n    let match;\n\n    while ((match = blockPattern.exec(this.file.content)) !== null) {\n      const blockName = match[1];\n      const blockContent = match[2];\n      \n      const block: Block = {\n        id: `block_${blockName}_${Date.now()}`,\n        name: blockName,\n        type: this.determineBlockType(blockContent),\n        tableName: this.extractTableName(blockContent),\n        items: this.extractItems(blockContent, blockName),\n        triggers: this.extractTriggers(blockContent, 'BLOCK', blockName),\n        properties: this.extractBlockProperties(blockContent)\n      };\n\n      blocks.push(block);\n    }\n\n    return blocks;\n  }\n\n  private determineBlockType(blockContent: string): 'DATA_BLOCK' | 'CONTROL_BLOCK' {\n    // Détermine si c'est un bloc de données ou de contrôle\n    if (blockContent.includes('TABLE_NAME') || blockContent.includes('QUERY_DATA_SOURCE_NAME')) {\n      return 'DATA_BLOCK';\n    }\n    return 'CONTROL_BLOCK';\n  }\n\n  private extractTableName(blockContent: string): string | undefined {\n    const tableMatch = blockContent.match(/TABLE_NAME\\s*=\\s*['\"]([^'\"]+)['\"]/i);\n    return tableMatch ? tableMatch[1] : undefined;\n  }\n\n  private extractItems(blockContent: string, blockName: string): Item[] {\n    const items: Item[] = [];\n    \n    // Pattern pour identifier les items dans un bloc\n    const itemPattern = /ITEM\\s+(\\w+)\\s*\\{([\\s\\S]*?)\\}/gi;\n    let match;\n\n    while ((match = itemPattern.exec(blockContent)) !== null) {\n      const itemName = match[1];\n      const itemContent = match[2];\n      \n      const item: Item = {\n        id: `item_${blockName}_${itemName}_${Date.now()}`,\n        name: itemName,\n        type: this.determineItemType(itemContent),\n        dataType: this.extractDataType(itemContent),\n        maxLength: this.extractMaxLength(itemContent),\n        required: this.isRequired(itemContent),\n        defaultValue: this.extractDefaultValue(itemContent),\n        triggers: this.extractTriggers(itemContent, 'ITEM', itemName),\n        properties: this.extractItemProperties(itemContent)\n      };\n\n      items.push(item);\n    }\n\n    return items;\n  }\n\n  private determineItemType(itemContent: string): Item['type'] {\n    if (itemContent.includes('ITEM_TYPE = TEXT_ITEM')) return 'TEXT_ITEM';\n    if (itemContent.includes('ITEM_TYPE = DISPLAY_ITEM')) return 'DISPLAY_ITEM';\n    if (itemContent.includes('ITEM_TYPE = LIST_ITEM')) return 'LIST_ITEM';\n    if (itemContent.includes('ITEM_TYPE = CHECKBOX')) return 'CHECKBOX';\n    if (itemContent.includes('ITEM_TYPE = RADIO_GROUP')) return 'RADIO_GROUP';\n    if (itemContent.includes('ITEM_TYPE = BUTTON')) return 'BUTTON';\n    return 'TEXT_ITEM'; // Par défaut\n  }\n\n  private extractDataType(itemContent: string): string | undefined {\n    const dataTypeMatch = itemContent.match(/DATA_TYPE\\s*=\\s*['\"]?([^'\"\\s]+)['\"]?/i);\n    return dataTypeMatch ? dataTypeMatch[1] : undefined;\n  }\n\n  private extractMaxLength(itemContent: string): number | undefined {\n    const maxLengthMatch = itemContent.match(/MAX_LENGTH\\s*=\\s*(\\d+)/i);\n    return maxLengthMatch ? parseInt(maxLengthMatch[1]) : undefined;\n  }\n\n  private isRequired(itemContent: string): boolean {\n    const requiredMatch = itemContent.match(/REQUIRED\\s*=\\s*(TRUE|YES|1)/i);\n    return !!requiredMatch;\n  }\n\n  private extractDefaultValue(itemContent: string): string | undefined {\n    const defaultMatch = itemContent.match(/DEFAULT_VALUE\\s*=\\s*['\"]([^'\"]+)['\"]/i);\n    return defaultMatch ? defaultMatch[1] : undefined;\n  }\n\n  private extractBlockProperties(blockContent: string): Record<string, any> {\n    const properties: Record<string, any> = {};\n    \n    // Extraction des propriétés communes des blocs\n    const propertyPatterns = [\n      { key: 'QUERY_DATA_SOURCE_NAME', pattern: /QUERY_DATA_SOURCE_NAME\\s*=\\s*['\"]([^'\"]+)['\"]/i },\n      { key: 'WHERE_CLAUSE', pattern: /WHERE_CLAUSE\\s*=\\s*['\"]([^'\"]+)['\"]/i },\n      { key: 'ORDER_BY_CLAUSE', pattern: /ORDER_BY_CLAUSE\\s*=\\s*['\"]([^'\"]+)['\"]/i },\n      { key: 'RECORDS_DISPLAYED', pattern: /RECORDS_DISPLAYED\\s*=\\s*(\\d+)/i },\n      { key: 'RECORDS_BUFFERED', pattern: /RECORDS_BUFFERED\\s*=\\s*(\\d+)/i }\n    ];\n\n    propertyPatterns.forEach(({ key, pattern }) => {\n      const match = blockContent.match(pattern);\n      if (match) {\n        properties[key] = isNaN(Number(match[1])) ? match[1] : Number(match[1]);\n      }\n    });\n\n    return properties;\n  }\n\n  private extractItemProperties(itemContent: string): Record<string, any> {\n    const properties: Record<string, any> = {};\n    \n    // Extraction des propriétés communes des items\n    const propertyPatterns = [\n      { key: 'X_POSITION', pattern: /X_POSITION\\s*=\\s*(\\d+)/i },\n      { key: 'Y_POSITION', pattern: /Y_POSITION\\s*=\\s*(\\d+)/i },\n      { key: 'WIDTH', pattern: /WIDTH\\s*=\\s*(\\d+)/i },\n      { key: 'HEIGHT', pattern: /HEIGHT\\s*=\\s*(\\d+)/i },\n      { key: 'PROMPT', pattern: /PROMPT\\s*=\\s*['\"]([^'\"]+)['\"]/i },\n      { key: 'TOOLTIP', pattern: /TOOLTIP\\s*=\\s*['\"]([^'\"]+)['\"]/i },\n      { key: 'ENABLED', pattern: /ENABLED\\s*=\\s*(TRUE|FALSE|YES|NO)/i },\n      { key: 'VISIBLE', pattern: /VISIBLE\\s*=\\s*(TRUE|FALSE|YES|NO)/i }\n    ];\n\n    propertyPatterns.forEach(({ key, pattern }) => {\n      const match = itemContent.match(pattern);\n      if (match) {\n        if (key === 'ENABLED' || key === 'VISIBLE') {\n          properties[key] = /TRUE|YES/i.test(match[1]);\n        } else {\n          properties[key] = isNaN(Number(match[1])) ? match[1] : Number(match[1]);\n        }\n      }\n    });\n\n    return properties;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,kBAAkB,uIAAA,CAAA,mBAAgB;IAC7C,MAAM,QAAmC;QACvC,MAAM,WAAW,IAAI,CAAC,eAAe;QACrC,MAAM,SAAS,IAAI,CAAC,aAAa;QACjC,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACzD,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QAC3D,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QACzD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QACzD,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QAE/D,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,gBAAyB;QAC/B,MAAM,SAAkB,EAAE;QAE1B,wDAAwD;QACxD,MAAM,eAAe;QACrB,IAAI;QAEJ,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAM;YAC9D,MAAM,YAAY,KAAK,CAAC,EAAE;YAC1B,MAAM,eAAe,KAAK,CAAC,EAAE;YAE7B,MAAM,QAAe;gBACnB,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,KAAK,GAAG,IAAI;gBACtC,MAAM;gBACN,MAAM,IAAI,CAAC,kBAAkB,CAAC;gBAC9B,WAAW,IAAI,CAAC,gBAAgB,CAAC;gBACjC,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc;gBACvC,UAAU,IAAI,CAAC,eAAe,CAAC,cAAc,SAAS;gBACtD,YAAY,IAAI,CAAC,sBAAsB,CAAC;YAC1C;YAEA,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;IACT;IAEQ,mBAAmB,YAAoB,EAAkC;QAC/E,uDAAuD;QACvD,IAAI,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,2BAA2B;YAC1F,OAAO;QACT;QACA,OAAO;IACT;IAEQ,iBAAiB,YAAoB,EAAsB;QACjE,MAAM,aAAa,aAAa,KAAK,CAAC;QACtC,OAAO,aAAa,UAAU,CAAC,EAAE,GAAG;IACtC;IAEQ,aAAa,YAAoB,EAAE,SAAiB,EAAU;QACpE,MAAM,QAAgB,EAAE;QAExB,iDAAiD;QACjD,MAAM,cAAc;QACpB,IAAI;QAEJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,aAAa,MAAM,KAAM;YACxD,MAAM,WAAW,KAAK,CAAC,EAAE;YACzB,MAAM,cAAc,KAAK,CAAC,EAAE;YAE5B,MAAM,OAAa;gBACjB,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI;gBACjD,MAAM;gBACN,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC7B,UAAU,IAAI,CAAC,eAAe,CAAC;gBAC/B,WAAW,IAAI,CAAC,gBAAgB,CAAC;gBACjC,UAAU,IAAI,CAAC,UAAU,CAAC;gBAC1B,cAAc,IAAI,CAAC,mBAAmB,CAAC;gBACvC,UAAU,IAAI,CAAC,eAAe,CAAC,aAAa,QAAQ;gBACpD,YAAY,IAAI,CAAC,qBAAqB,CAAC;YACzC;YAEA,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEQ,kBAAkB,WAAmB,EAAgB;QAC3D,IAAI,YAAY,QAAQ,CAAC,0BAA0B,OAAO;QAC1D,IAAI,YAAY,QAAQ,CAAC,6BAA6B,OAAO;QAC7D,IAAI,YAAY,QAAQ,CAAC,0BAA0B,OAAO;QAC1D,IAAI,YAAY,QAAQ,CAAC,yBAAyB,OAAO;QACzD,IAAI,YAAY,QAAQ,CAAC,4BAA4B,OAAO;QAC5D,IAAI,YAAY,QAAQ,CAAC,uBAAuB,OAAO;QACvD,OAAO,aAAa,aAAa;IACnC;IAEQ,gBAAgB,WAAmB,EAAsB;QAC/D,MAAM,gBAAgB,YAAY,KAAK,CAAC;QACxC,OAAO,gBAAgB,aAAa,CAAC,EAAE,GAAG;IAC5C;IAEQ,iBAAiB,WAAmB,EAAsB;QAChE,MAAM,iBAAiB,YAAY,KAAK,CAAC;QACzC,OAAO,iBAAiB,SAAS,cAAc,CAAC,EAAE,IAAI;IACxD;IAEQ,WAAW,WAAmB,EAAW;QAC/C,MAAM,gBAAgB,YAAY,KAAK,CAAC;QACxC,OAAO,CAAC,CAAC;IACX;IAEQ,oBAAoB,WAAmB,EAAsB;QACnE,MAAM,eAAe,YAAY,KAAK,CAAC;QACvC,OAAO,eAAe,YAAY,CAAC,EAAE,GAAG;IAC1C;IAEQ,uBAAuB,YAAoB,EAAuB;QACxE,MAAM,aAAkC,CAAC;QAEzC,+CAA+C;QAC/C,MAAM,mBAAmB;YACvB;gBAAE,KAAK;gBAA0B,SAAS;YAAiD;YAC3F;gBAAE,KAAK;gBAAgB,SAAS;YAAuC;YACvE;gBAAE,KAAK;gBAAmB,SAAS;YAA0C;YAC7E;gBAAE,KAAK;gBAAqB,SAAS;YAAiC;YACtE;gBAAE,KAAK;gBAAoB,SAAS;YAAgC;SACrE;QAED,iBAAiB,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE;YACxC,MAAM,QAAQ,aAAa,KAAK,CAAC;YACjC,IAAI,OAAO;gBACT,UAAU,CAAC,IAAI,GAAG,MAAM,OAAO,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,GAAG,OAAO,KAAK,CAAC,EAAE;YACxE;QACF;QAEA,OAAO;IACT;IAEQ,sBAAsB,WAAmB,EAAuB;QACtE,MAAM,aAAkC,CAAC;QAEzC,+CAA+C;QAC/C,MAAM,mBAAmB;YACvB;gBAAE,KAAK;gBAAc,SAAS;YAA0B;YACxD;gBAAE,KAAK;gBAAc,SAAS;YAA0B;YACxD;gBAAE,KAAK;gBAAS,SAAS;YAAqB;YAC9C;gBAAE,KAAK;gBAAU,SAAS;YAAsB;YAChD;gBAAE,KAAK;gBAAU,SAAS;YAAiC;YAC3D;gBAAE,KAAK;gBAAW,SAAS;YAAkC;YAC7D;gBAAE,KAAK;gBAAW,SAAS;YAAqC;YAChE;gBAAE,KAAK;gBAAW,SAAS;YAAqC;SACjE;QAED,iBAAiB,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE;YACxC,MAAM,QAAQ,YAAY,KAAK,CAAC;YAChC,IAAI,OAAO;gBACT,IAAI,QAAQ,aAAa,QAAQ,WAAW;oBAC1C,UAAU,CAAC,IAAI,GAAG,YAAY,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC7C,OAAO;oBACL,UAAU,CAAC,IAAI,GAAG,MAAM,OAAO,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,GAAG,OAAO,KAAK,CAAC,EAAE;gBACxE;YACF;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 3110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/components/oracle-forms-editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';\nimport { FileUploader } from '@/components/file-uploader';\nimport { CodeEditor } from '@/components/code-editor';\nimport { AIChat } from '@/components/ai-chat';\nimport { AnalysisPanel } from '@/components/analysis-panel';\nimport { OracleFormsFile, ParsedOracleFile, AIAnalysis } from '@/types/oracle-forms';\nimport { FMBParser } from '@/lib/parsers/fmb-parser';\nimport { GroqAIService } from '@/lib/ai/groq-service';\n\nexport function OracleFormsEditor() {\n  const [files, setFiles] = useState<OracleFormsFile[]>([]);\n  const [parsedFiles, setParsedFiles] = useState<ParsedOracleFile[]>([]);\n  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);\n  const [analyses, setAnalyses] = useState<AIAnalysis[]>([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [groqApiKey, setGroqApiKey] = useState<string>('');\n\n  const selectedFile = files.find(f => f.id === selectedFileId);\n  const selectedParsedFile = parsedFiles.find(f => \n    files.find(file => file.id === selectedFileId)?.id === selectedFileId\n  );\n\n  const handleFilesUploaded = useCallback(async (uploadedFiles: OracleFormsFile[]) => {\n    setFiles(prev => [...prev, ...uploadedFiles]);\n    \n    // Parse les nouveaux fichiers\n    const newParsedFiles: ParsedOracleFile[] = [];\n    \n    for (const file of uploadedFiles) {\n      try {\n        let parser;\n        switch (file.type) {\n          case 'FMB':\n            parser = new FMBParser(file);\n            break;\n          // TODO: Ajouter d'autres parsers pour RDF, PLL, OLB\n          default:\n            console.warn(`Parser non implémenté pour le type ${file.type}`);\n            continue;\n        }\n        \n        const parsed = await parser.parse();\n        newParsedFiles.push(parsed);\n      } catch (error) {\n        console.error(`Erreur lors du parsing de ${file.name}:`, error);\n      }\n    }\n    \n    setParsedFiles(prev => [...prev, ...newParsedFiles]);\n    \n    // Sélectionner le premier fichier si aucun n'est sélectionné\n    if (!selectedFileId && uploadedFiles.length > 0) {\n      setSelectedFileId(uploadedFiles[0].id);\n    }\n  }, [selectedFileId]);\n\n  const handleAnalyzeFile = useCallback(async (\n    fileId: string, \n    analysisType: 'CODE_REVIEW' | 'DOCUMENTATION' | 'REFACTORING' | 'OPTIMIZATION'\n  ) => {\n    if (!groqApiKey) {\n      alert('Veuillez configurer votre clé API Groq dans les paramètres');\n      return;\n    }\n\n    const file = files.find(f => f.id === fileId);\n    const parsedFile = parsedFiles.find((_, index) => files[index]?.id === fileId);\n    \n    if (!file || !parsedFile) {\n      console.error('Fichier ou données parsées non trouvés');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    \n    try {\n      const aiService = new GroqAIService(groqApiKey);\n      const analysis = await aiService.analyzeOracleFormsCode(file, parsedFile, analysisType);\n      \n      setAnalyses(prev => [...prev, analysis]);\n    } catch (error) {\n      console.error('Erreur lors de l\\'analyse:', error);\n      alert('Erreur lors de l\\'analyse du fichier');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [files, parsedFiles, groqApiKey]);\n\n  const handleGenerateDocumentation = useCallback(async (fileId: string) => {\n    if (!groqApiKey) {\n      alert('Veuillez configurer votre clé API Groq dans les paramètres');\n      return;\n    }\n\n    const file = files.find(f => f.id === fileId);\n    const parsedFile = parsedFiles.find((_, index) => files[index]?.id === fileId);\n    \n    if (!file || !parsedFile) {\n      console.error('Fichier ou données parsées non trouvés');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    \n    try {\n      const aiService = new GroqAIService(groqApiKey);\n      const documentation = await aiService.generateDocumentation(file, parsedFile);\n      \n      // TODO: Afficher la documentation dans un modal ou un nouvel onglet\n      console.log('Documentation générée:', documentation);\n      alert('Documentation générée avec succès ! Consultez la console pour voir le résultat.');\n    } catch (error) {\n      console.error('Erreur lors de la génération de documentation:', error);\n      alert('Erreur lors de la génération de documentation');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [files, parsedFiles, groqApiKey]);\n\n  return (\n    <div className=\"h-[calc(100vh-120px)] flex flex-col\">\n      {/* Configuration API Key */}\n      {!groqApiKey && (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-sm font-medium text-yellow-800\">\n                Configuration requise\n              </h3>\n              <p className=\"text-sm text-yellow-700 mt-1\">\n                Veuillez configurer votre clé API Groq pour utiliser les fonctionnalités IA.\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <input\n                type=\"password\"\n                placeholder=\"Clé API Groq\"\n                className=\"px-3 py-2 border border-gray-300 rounded-md text-sm\"\n                onChange={(e) => setGroqApiKey(e.target.value)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      <Tabs defaultValue=\"files\" className=\"flex-1 flex flex-col\">\n        <TabsList className=\"grid w-full grid-cols-4\">\n          <TabsTrigger value=\"files\">Fichiers</TabsTrigger>\n          <TabsTrigger value=\"editor\">Éditeur</TabsTrigger>\n          <TabsTrigger value=\"analysis\">Analyse</TabsTrigger>\n          <TabsTrigger value=\"chat\">Assistant IA</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"files\" className=\"flex-1\">\n          <FileUploader \n            onFilesUploaded={handleFilesUploaded}\n            files={files}\n            onFileSelect={setSelectedFileId}\n            selectedFileId={selectedFileId}\n            onAnalyzeFile={handleAnalyzeFile}\n            onGenerateDocumentation={handleGenerateDocumentation}\n            isAnalyzing={isAnalyzing}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"editor\" className=\"flex-1\">\n          {selectedFile ? (\n            <CodeEditor \n              file={selectedFile}\n              parsedFile={selectedParsedFile}\n            />\n          ) : (\n            <div className=\"flex items-center justify-center h-full text-gray-500\">\n              <p>Sélectionnez un fichier pour commencer l'édition</p>\n            </div>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"analysis\" className=\"flex-1\">\n          <AnalysisPanel \n            analyses={analyses}\n            files={files}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"chat\" className=\"flex-1\">\n          <AIChat \n            files={files}\n            parsedFiles={parsedFiles}\n            groqApiKey={groqApiKey}\n          />\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAVA;;;;;;;;;;AAYO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC9C,MAAM,qBAAqB,YAAY,IAAI,CAAC,CAAA,IAC1C,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,iBAAiB,OAAO;IAGzD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,SAAS,CAAA,OAAQ;mBAAI;mBAAS;aAAc;QAE5C,8BAA8B;QAC9B,MAAM,iBAAqC,EAAE;QAE7C,KAAK,MAAM,QAAQ,cAAe;YAChC,IAAI;gBACF,IAAI;gBACJ,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,SAAS,IAAI,sIAAA,CAAA,YAAS,CAAC;wBACvB;oBACF,oDAAoD;oBACpD;wBACE,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;wBAC9D;gBACJ;gBAEA,MAAM,SAAS,MAAM,OAAO,KAAK;gBACjC,eAAe,IAAI,CAAC;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YAC3D;QACF;QAEA,eAAe,CAAA,OAAQ;mBAAI;mBAAS;aAAe;QAEnD,6DAA6D;QAC7D,IAAI,CAAC,kBAAkB,cAAc,MAAM,GAAG,GAAG;YAC/C,kBAAkB,aAAa,CAAC,EAAE,CAAC,EAAE;QACvC;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACpC,QACA;QAEA,IAAI,CAAC,YAAY;YACf,MAAM;YACN;QACF;QAEA,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtC,MAAM,aAAa,YAAY,IAAI,CAAC,CAAC,GAAG,QAAU,KAAK,CAAC,MAAM,EAAE,OAAO;QAEvE,IAAI,CAAC,QAAQ,CAAC,YAAY;YACxB,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,eAAe;QAEf,IAAI;YACF,MAAM,YAAY,IAAI,mIAAA,CAAA,gBAAa,CAAC;YACpC,MAAM,WAAW,MAAM,UAAU,sBAAsB,CAAC,MAAM,YAAY;YAE1E,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAO;QAAa;KAAW;IAEnC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrD,IAAI,CAAC,YAAY;YACf,MAAM;YACN;QACF;QAEA,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtC,MAAM,aAAa,YAAY,IAAI,CAAC,CAAC,GAAG,QAAU,KAAK,CAAC,MAAM,EAAE,OAAO;QAEvE,IAAI,CAAC,QAAQ,CAAC,YAAY;YACxB,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,eAAe;QAEf,IAAI;YACF,MAAM,YAAY,IAAI,mIAAA,CAAA,gBAAa,CAAC;YACpC,MAAM,gBAAgB,MAAM,UAAU,qBAAqB,CAAC,MAAM;YAElE,oEAAoE;YACpE,QAAQ,GAAG,CAAC,0BAA0B;YACtC,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAO;QAAa;KAAW;IAEnC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,4BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAA+B;;;;;;;;;;;;sCAI9C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAOvD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAQ,WAAU;;kCACnC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAQ;;;;;;0CAC3B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;;;;;;;kCAG5B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,8OAAC,sIAAA,CAAA,eAAY;4BACX,iBAAiB;4BACjB,OAAO;4BACP,cAAc;4BACd,gBAAgB;4BAChB,eAAe;4BACf,yBAAyB;4BACzB,aAAa;;;;;;;;;;;kCAIjB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACnC,6BACC,8OAAC,oIAAA,CAAA,aAAU;4BACT,MAAM;4BACN,YAAY;;;;;iDAGd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;kCAKT,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,8OAAC,uIAAA,CAAA,gBAAa;4BACZ,UAAU;4BACV,OAAO;;;;;;;;;;;kCAIX,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;kCAClC,cAAA,8OAAC,gIAAA,CAAA,SAAM;4BACL,OAAO;4BACP,aAAa;4BACb,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}, {"offset": {"line": 3443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20BCT/Bureau/AI%20editor%20%20Oracle%20Forms/oracle-forms-ai-assistant/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { OracleFormsEditor } from '@/components/oracle-forms-editor';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Oracle Forms AI Assistant\n              </h1>\n              <span className=\"px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full\">\n                v1.0.0\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-500\">\n                Assistant IA pour Oracle Forms\n              </span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <OracleFormsEditor />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,8OAAC;wCAAK,WAAU;kDAAuE;;;;;;;;;;;;0CAIzF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,+IAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;AAI1B", "debugId": null}}]}