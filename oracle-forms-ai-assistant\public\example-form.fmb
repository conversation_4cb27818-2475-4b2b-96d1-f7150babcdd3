-- Exemple de fichier Oracle Forms (FMB) pour test
-- Formulaire de gestion des employés

VERSION = "********.0"
FORM_NAME = "EMP_FORM"
DESCRIPTION = "Formulaire de gestion des employés"
AUTHOR = "Développeur Oracle"
CREATION_DATE = "2024-01-15"

-- Bloc principal des employés
BLOCK EMP_BLOCK {
    TABLE_NAME = "EMPLOYEES"
    QUERY_DATA_SOURCE_NAME = "EMPLOYEES"
    WHERE_CLAUSE = "DEPARTMENT_ID = :GLOBAL.DEPT_ID"
    ORDER_BY_CLAUSE = "LAST_NAME, FIRST_NAME"
    RECORDS_DISPLAYED = 10
    RECORDS_BUFFERED = 20
    
    -- Item ID employé
    ITEM EMP_ID {
        ITEM_TYPE = DISPLAY_ITEM
        DATA_TYPE = NUMBER
        MAX_LENGTH = 10
        REQUIRED = TRUE
        X_POSITION = 10
        Y_POSITION = 10
        WIDTH = 100
        HEIGHT = 25
        PROMPT = "ID Employé:"
        
        TRIGGER WHEN-VALIDATE-ITEM
            IF :EMP_BLOCK.EMP_ID IS NULL THEN
                MESSAGE('L''ID employé est obligatoire');
                RAISE FORM_TRIGGER_FAILURE;
            END IF;
        END TRIGGER
    }
    
    -- Item nom de famille
    ITEM LAST_NAME {
        ITEM_TYPE = TEXT_ITEM
        DATA_TYPE = VARCHAR2
        MAX_LENGTH = 50
        REQUIRED = TRUE
        X_POSITION = 10
        Y_POSITION = 40
        WIDTH = 200
        HEIGHT = 25
        PROMPT = "Nom:"
        
        TRIGGER WHEN-VALIDATE-ITEM
            IF LENGTH(:EMP_BLOCK.LAST_NAME) < 2 THEN
                MESSAGE('Le nom doit contenir au moins 2 caractères');
                RAISE FORM_TRIGGER_FAILURE;
            END IF;
        END TRIGGER
    }
    
    -- Item prénom
    ITEM FIRST_NAME {
        ITEM_TYPE = TEXT_ITEM
        DATA_TYPE = VARCHAR2
        MAX_LENGTH = 50
        REQUIRED = TRUE
        X_POSITION = 10
        Y_POSITION = 70
        WIDTH = 200
        HEIGHT = 25
        PROMPT = "Prénom:"
    }
    
    -- Item email
    ITEM EMAIL {
        ITEM_TYPE = TEXT_ITEM
        DATA_TYPE = VARCHAR2
        MAX_LENGTH = 100
        REQUIRED = TRUE
        X_POSITION = 10
        Y_POSITION = 100
        WIDTH = 250
        HEIGHT = 25
        PROMPT = "Email:"
        
        TRIGGER WHEN-VALIDATE-ITEM
            IF INSTR(:EMP_BLOCK.EMAIL, '@') = 0 THEN
                MESSAGE('Format d''email invalide');
                RAISE FORM_TRIGGER_FAILURE;
            END IF;
        END TRIGGER
    }
    
    -- Item salaire
    ITEM SALARY {
        ITEM_TYPE = TEXT_ITEM
        DATA_TYPE = NUMBER
        MAX_LENGTH = 10
        X_POSITION = 10
        Y_POSITION = 130
        WIDTH = 150
        HEIGHT = 25
        PROMPT = "Salaire:"
        
        TRIGGER WHEN-VALIDATE-ITEM
            IF :EMP_BLOCK.SALARY < 0 THEN
                MESSAGE('Le salaire ne peut pas être négatif');
                RAISE FORM_TRIGGER_FAILURE;
            END IF;
        END TRIGGER
    }
    
    -- Item département
    ITEM DEPARTMENT_ID {
        ITEM_TYPE = LIST_ITEM
        DATA_TYPE = NUMBER
        MAX_LENGTH = 10
        X_POSITION = 10
        Y_POSITION = 160
        WIDTH = 200
        HEIGHT = 25
        PROMPT = "Département:"
    }
    
    -- Trigger de bloc
    TRIGGER PRE-QUERY
        IF :GLOBAL.DEPT_ID IS NULL THEN
            MESSAGE('Veuillez sélectionner un département');
            RAISE FORM_TRIGGER_FAILURE;
        END IF;
    END TRIGGER
    
    TRIGGER POST-QUERY
        -- Calcul du bonus basé sur le salaire
        IF :EMP_BLOCK.SALARY > 50000 THEN
            :EMP_BLOCK.BONUS := :EMP_BLOCK.SALARY * 0.1;
        ELSE
            :EMP_BLOCK.BONUS := :EMP_BLOCK.SALARY * 0.05;
        END IF;
    END TRIGGER
}

-- Bloc de contrôle pour les boutons
BLOCK CONTROL_BLOCK {
    ITEM BTN_SAVE {
        ITEM_TYPE = BUTTON
        X_POSITION = 10
        Y_POSITION = 200
        WIDTH = 80
        HEIGHT = 30
        PROMPT = "Sauvegarder"
        
        TRIGGER WHEN-BUTTON-PRESSED
            COMMIT_FORM;
            MESSAGE('Données sauvegardées avec succès');
        END TRIGGER
    }
    
    ITEM BTN_CANCEL {
        ITEM_TYPE = BUTTON
        X_POSITION = 100
        Y_POSITION = 200
        WIDTH = 80
        HEIGHT = 30
        PROMPT = "Annuler"
        
        TRIGGER WHEN-BUTTON-PRESSED
            ROLLBACK_FORM;
            MESSAGE('Modifications annulées');
        END TRIGGER
    }
}

-- Triggers de formulaire
TRIGGER WHEN-NEW-FORM-INSTANCE
    -- Initialisation du formulaire
    :GLOBAL.DEPT_ID := 10; -- Département par défaut
    GO_BLOCK('EMP_BLOCK');
    EXECUTE_QUERY;
END TRIGGER

TRIGGER KEY-EXIT
    IF FORM_SUCCESS THEN
        EXIT_FORM;
    ELSE
        MESSAGE('Impossible de quitter le formulaire');
    END IF;
END TRIGGER

-- Procédures personnalisées
PROCEDURE VALIDATE_EMPLOYEE IS
BEGIN
    -- Validation globale de l'employé
    IF :EMP_BLOCK.LAST_NAME IS NULL OR :EMP_BLOCK.FIRST_NAME IS NULL THEN
        MESSAGE('Le nom et prénom sont obligatoires');
        RAISE FORM_TRIGGER_FAILURE;
    END IF;
    
    IF :EMP_BLOCK.EMAIL IS NULL THEN
        MESSAGE('L''email est obligatoire');
        RAISE FORM_TRIGGER_FAILURE;
    END IF;
    
    -- Vérification de l'unicité de l'email
    DECLARE
        v_count NUMBER;
    BEGIN
        SELECT COUNT(*)
        INTO v_count
        FROM EMPLOYEES
        WHERE EMAIL = :EMP_BLOCK.EMAIL
        AND EMPLOYEE_ID != NVL(:EMP_BLOCK.EMP_ID, -1);
        
        IF v_count > 0 THEN
            MESSAGE('Cet email est déjà utilisé par un autre employé');
            RAISE FORM_TRIGGER_FAILURE;
        END IF;
    END;
END VALIDATE_EMPLOYEE;

FUNCTION CALCULATE_BONUS(p_salary NUMBER) RETURN NUMBER IS
    v_bonus NUMBER;
BEGIN
    IF p_salary > 100000 THEN
        v_bonus := p_salary * 0.15;
    ELSIF p_salary > 50000 THEN
        v_bonus := p_salary * 0.1;
    ELSE
        v_bonus := p_salary * 0.05;
    END IF;
    
    RETURN v_bonus;
END CALCULATE_BONUS;

-- Variables globales
GLOBAL_VAR g_current_user VARCHAR2(50);
GLOBAL_VAR g_session_id NUMBER;
GLOBAL_VAR g_dept_id NUMBER;
