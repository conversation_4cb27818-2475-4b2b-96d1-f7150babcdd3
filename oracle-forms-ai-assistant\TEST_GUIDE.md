# Guide de test - Oracle Forms AI Assistant

## 🧪 Tests de fonctionnement

### 1. Test de l'interface utilisateur

✅ **Vérifications de base :**
- [ ] L'application se charge correctement sur http://localhost:3000
- [ ] L'en-tête affiche "Oracle Forms AI Assistant v1.0.0"
- [ ] Les 4 onglets sont visibles : <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Assistant IA
- [ ] La bannière de configuration API apparaît (jaune)

### 2. Test de configuration API

✅ **Configuration Groq :**
- [ ] Entrer une clé API Groq valide dans le champ
- [ ] La bannière jaune disparaît après configuration
- [ ] Aucune erreur dans la console du navigateur

**Note :** Le modèle utilisé est maintenant `meta-llama/llama-4-scout-17b-16e-instruct` (Llama 4 Scout)

### 3. Test de chargement de fichiers

✅ **Fichier d'exemple fourni :**
- [ ] Utiliser le fichier `public/example-form.fmb`
- [ ] Glisser-déposer le fichier dans la zone de drop
- [ ] Le fichier apparaît dans la liste avec les bonnes informations
- [ ] Cliquer sur le fichier pour le sélectionner

✅ **Formats supportés :**
- [ ] .fmb (Oracle Forms) ✅ Implémenté
- [ ] .rdf (Oracle Reports) ⚠️ Parser en développement
- [ ] .pll (Bibliothèques PL/SQL) ⚠️ Parser en développement
- [ ] .olb (Bibliothèques d'objets) ⚠️ Parser en développement

### 4. Test de l'éditeur de code

✅ **Onglet Éditeur :**
- [ ] Sélectionner un fichier FMB
- [ ] L'éditeur Monaco s'affiche avec coloration syntaxique
- [ ] Les sous-onglets sont disponibles : Code Source, Structure, Triggers, Procédures
- [ ] Navigation fluide entre les sous-onglets

✅ **Sous-onglet Structure :**
- [ ] Métadonnées du fichier affichées
- [ ] Liste des blocs avec types (DATA_BLOCK/CONTROL_BLOCK)
- [ ] Dépendances listées

✅ **Sous-onglet Triggers :**
- [ ] Liste des triggers avec leur code
- [ ] Niveau de trigger affiché (FORM/BLOCK/ITEM)

✅ **Sous-onglet Procédures :**
- [ ] Procédures et fonctions listées
- [ ] Paramètres affichés avec types et modes
- [ ] Code source visible

### 5. Test d'analyse IA

✅ **Prérequis :**
- [ ] Clé API Groq configurée
- [ ] Fichier FMB chargé et sélectionné

✅ **Lancement d'analyse :**
- [ ] Cliquer sur "Analyser" dans l'onglet Fichiers
- [ ] Indicateur de chargement affiché
- [ ] Analyse apparaît dans l'onglet "Analyse"

✅ **Types d'analyse disponibles :**
- [ ] CODE_REVIEW (Revue de Code)
- [ ] DOCUMENTATION (Documentation)
- [ ] REFACTORING (Refactorisation)
- [ ] OPTIMIZATION (Optimisation)

✅ **Résultats d'analyse :**
- [ ] Résumé de l'analyse
- [ ] Score de qualité (si disponible)
- [ ] Problèmes identifiés avec niveaux de sévérité
- [ ] Suggestions d'amélioration avec code avant/après

### 6. Test du chat IA

✅ **Interface de chat :**
- [ ] Onglet "Assistant IA" accessible
- [ ] Message de bienvenue affiché
- [ ] Questions suggérées visibles
- [ ] Zone de saisie fonctionnelle

✅ **Interaction avec l'IA :**
- [ ] Poser une question simple : "Bonjour, peux-tu m'aider ?"
- [ ] Réponse de l'IA reçue
- [ ] Historique des messages conservé
- [ ] Contexte des fichiers mentionné

✅ **Questions de test suggérées :**
```
1. "Peux-tu analyser la structure de mon formulaire ?"
2. "Quels sont les meilleures pratiques pour les triggers Oracle Forms ?"
3. "Comment optimiser les performances de mes requêtes ?"
4. "Explique-moi le trigger WHEN-VALIDATE-ITEM dans mon code"
5. "Comment améliorer la sécurité de mon application ?"
```

### 7. Test de génération de documentation

✅ **Génération de documentation :**
- [ ] Cliquer sur "Documenter" dans l'onglet Fichiers
- [ ] Indicateur de chargement affiché
- [ ] Documentation générée (vérifier dans la console pour l'instant)
- [ ] Aucune erreur dans la console

### 8. Tests d'erreur et de robustesse

✅ **Gestion d'erreurs :**
- [ ] Tenter d'analyser sans clé API → Message d'erreur approprié
- [ ] Charger un fichier non supporté → Message d'erreur
- [ ] Tester avec une clé API invalide → Gestion d'erreur gracieuse

✅ **Performance :**
- [ ] Chargement de fichiers volumineux (>1MB)
- [ ] Réactivité de l'interface pendant les analyses
- [ ] Pas de blocage de l'interface utilisateur

## 🐛 Problèmes connus et limitations

### Limitations actuelles :
1. **Parsers incomplets** : Seuls les fichiers FMB sont entièrement supportés
2. **Sauvegarde** : Les modifications ne peuvent pas encore être sauvegardées
3. **Persistance** : Les analyses ne sont pas sauvegardées entre les sessions
4. **Export** : La documentation générée n'est pas exportable (console uniquement)

### Problèmes potentiels :
1. **Clé API** : Stockée en mémoire, perdue au rafraîchissement
2. **Gros fichiers** : Peuvent causer des problèmes de performance
3. **Modèle IA** : Réponses peuvent varier selon la charge du service Groq

## 📊 Critères de réussite

### ✅ Tests réussis si :
- [ ] Toutes les fonctionnalités de base fonctionnent
- [ ] L'analyse IA produit des résultats cohérents
- [ ] Le chat IA répond de manière pertinente
- [ ] Aucune erreur critique dans la console
- [ ] Interface responsive et fluide

### ⚠️ Tests partiels si :
- [ ] Quelques fonctionnalités mineures ne marchent pas
- [ ] Performance dégradée mais utilisable
- [ ] Erreurs non-critiques occasionnelles

### ❌ Tests échoués si :
- [ ] Impossible de charger l'application
- [ ] Erreurs critiques empêchant l'utilisation
- [ ] Aucune réponse de l'API IA
- [ ] Interface complètement cassée

## 🔧 Dépannage rapide

### Problème : L'application ne se charge pas
**Solution :** 
```bash
cd oracle-forms-ai-assistant
npm install
npm run dev
```

### Problème : Erreur de modèle IA
**Solution :** Vérifier que le modèle `meta-llama/llama-4-scout-17b-16e-instruct` est bien utilisé dans `src/lib/ai/groq-service.ts`

### Problème : Clé API ne fonctionne pas
**Solution :** 
1. Vérifier la clé sur https://console.groq.com
2. S'assurer qu'elle commence par `gsk_`
3. Vérifier les quotas et limites

### Problème : Fichier ne se charge pas
**Solution :** 
1. Vérifier l'extension (.fmb supporté)
2. Taille du fichier raisonnable (<10MB)
3. Fichier non corrompu

---

**Date de dernière mise à jour :** 28 Mai 2025
**Version testée :** v1.0.0 avec Llama 4 Scout
**Modèle IA :** meta-llama/llama-4-scout-17b-16e-instruct
