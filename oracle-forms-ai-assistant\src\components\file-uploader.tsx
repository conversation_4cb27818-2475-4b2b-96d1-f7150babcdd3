'use client';

import { useCallback, useState } from 'react';
import { Upload, File, Play, FileText, Loader2 } from 'lucide-react';
import { OracleFormsFile } from '@/types/oracle-forms';

interface FileUploaderProps {
  onFilesUploaded: (files: OracleFormsFile[]) => void;
  files: OracleFormsFile[];
  onFileSelect: (fileId: string) => void;
  selectedFileId: string | null;
  onAnalyzeFile: (fileId: string, analysisType: 'CODE_REVIEW' | 'DOCUMENTATION' | 'REFACTORING' | 'OPTIMIZATION') => void;
  onGenerateDocumentation: (fileId: string) => void;
  isAnalyzing: boolean;
}

export function FileUploader({
  onFilesUploaded,
  files,
  onFileSelect,
  selectedFileId,
  onAnalyzeFile,
  onGenerateDocumentation,
  isAnalyzing
}: FileUploaderProps) {
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, []);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  }, []);

  const handleFiles = useCallback(async (fileList: FileList) => {
    const newFiles: OracleFormsFile[] = [];
    
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const extension = file.name.split('.').pop()?.toUpperCase();
      
      if (!['FMB', 'RDF', 'PLL', 'OLB'].includes(extension || '')) {
        alert(`Type de fichier non supporté: ${file.name}`);
        continue;
      }
      
      try {
        const content = await file.text();
        const oracleFile: OracleFormsFile = {
          id: `${Date.now()}_${i}`,
          name: file.name,
          type: extension as 'FMB' | 'RDF' | 'PLL' | 'OLB',
          content,
          size: file.size,
          lastModified: new Date(file.lastModified)
        };
        
        newFiles.push(oracleFile);
      } catch (error) {
        console.error(`Erreur lors de la lecture du fichier ${file.name}:`, error);
        alert(`Erreur lors de la lecture du fichier ${file.name}`);
      }
    }
    
    if (newFiles.length > 0) {
      onFilesUploaded(newFiles);
    }
  }, [onFilesUploaded]);

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'FMB':
        return '📋';
      case 'RDF':
        return '📊';
      case 'PLL':
        return '📚';
      case 'OLB':
        return '🔧';
      default:
        return '📄';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Zone de drop */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          multiple
          accept=".fmb,.rdf,.pll,.olb"
          onChange={handleChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
        
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Glissez-déposez vos fichiers Oracle Forms
        </h3>
        <p className="text-sm text-gray-500 mb-4">
          Ou cliquez pour sélectionner des fichiers
        </p>
        <p className="text-xs text-gray-400">
          Formats supportés: FMB, RDF, PLL, OLB
        </p>
      </div>

      {/* Liste des fichiers */}
      {files.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            Fichiers chargés ({files.length})
          </h3>
          
          <div className="grid gap-4">
            {files.map((file) => (
              <div
                key={file.id}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedFileId === file.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => onFileSelect(file.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getFileIcon(file.type)}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{file.name}</h4>
                      <p className="text-sm text-gray-500">
                        {file.type} • {formatFileSize(file.size)} • 
                        Modifié le {file.lastModified.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onAnalyzeFile(file.id, 'CODE_REVIEW');
                      }}
                      disabled={isAnalyzing}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50"
                    >
                      {isAnalyzing ? (
                        <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      ) : (
                        <Play className="h-3 w-3 mr-1" />
                      )}
                      Analyser
                    </button>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onGenerateDocumentation(file.id);
                      }}
                      disabled={isAnalyzing}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 disabled:opacity-50"
                    >
                      {isAnalyzing ? (
                        <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      ) : (
                        <FileText className="h-3 w-3 mr-1" />
                      )}
                      Documenter
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
