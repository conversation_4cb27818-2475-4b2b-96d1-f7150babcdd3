'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Info, Lightbulb, Clock, FileText } from 'lucide-react';
import { AIAnalysis, OracleFormsFile, Finding, Suggestion } from '@/types/oracle-forms';

interface AnalysisPanelProps {
  analyses: AIAnalysis[];
  files: OracleFormsFile[];
}

export function AnalysisPanel({ analyses, files }: AnalysisPanelProps) {
  const [selectedAnalysisId, setSelectedAnalysisId] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<string>('all');

  const selectedAnalysis = analyses.find(a => a.id === selectedAnalysisId);

  const getAnalysisTypeLabel = (type: string) => {
    switch (type) {
      case 'CODE_REVIEW':
        return 'Revue de Code';
      case 'DOCUMENTATION':
        return 'Documentation';
      case 'REFACTORING':
        return 'Refactorisation';
      case 'OPTIMIZATION':
        return 'Optimisation';
      default:
        return type;
    }
  };

  const getAnalysisTypeColor = (type: string) => {
    switch (type) {
      case 'CODE_REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'DOCUMENTATION':
        return 'bg-green-100 text-green-800';
      case 'REFACTORING':
        return 'bg-yellow-100 text-yellow-800';
      case 'OPTIMIZATION':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'HIGH':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'MEDIUM':
        return <Info className="h-4 w-4 text-yellow-500" />;
      case 'LOW':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'HIGH':
        return 'border-red-200 bg-red-50';
      case 'MEDIUM':
        return 'border-yellow-200 bg-yellow-50';
      case 'LOW':
        return 'border-green-200 bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getFileName = (fileId: string) => {
    const file = files.find(f => f.id === fileId);
    return file ? file.name : 'Fichier inconnu';
  };

  const filteredAnalyses = analyses.filter(analysis => {
    if (filterType === 'all') return true;
    return analysis.type === filterType;
  });

  if (analyses.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-lg font-medium mb-2">Aucune analyse disponible</p>
          <p className="text-sm">
            Sélectionnez un fichier et lancez une analyse pour voir les résultats ici.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex">
      {/* Liste des analyses */}
      <div className="w-1/3 border-r bg-gray-50 overflow-auto">
        <div className="p-4 border-b bg-white">
          <h2 className="text-lg font-semibold mb-3">Analyses ({analyses.length})</h2>
          
          {/* Filtres */}
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">Tous les types</option>
            <option value="CODE_REVIEW">Revue de Code</option>
            <option value="DOCUMENTATION">Documentation</option>
            <option value="REFACTORING">Refactorisation</option>
            <option value="OPTIMIZATION">Optimisation</option>
          </select>
        </div>

        <div className="p-4 space-y-3">
          {filteredAnalyses.map((analysis) => (
            <div
              key={analysis.id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedAnalysisId === analysis.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
              onClick={() => setSelectedAnalysisId(analysis.id)}
            >
              <div className="flex items-center justify-between mb-2">
                <span className={`px-2 py-1 text-xs font-medium rounded ${getAnalysisTypeColor(analysis.type)}`}>
                  {getAnalysisTypeLabel(analysis.type)}
                </span>
                <div className="flex items-center text-xs text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {analysis.timestamp.toLocaleDateString()}
                </div>
              </div>
              
              <h3 className="font-medium text-sm mb-1">{getFileName(analysis.fileId)}</h3>
              <p className="text-xs text-gray-600 line-clamp-2">{analysis.summary}</p>
              
              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <span>{analysis.findings.length} problèmes</span>
                <span>{analysis.suggestions.length} suggestions</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Détails de l'analyse sélectionnée */}
      <div className="flex-1 overflow-auto">
        {selectedAnalysis ? (
          <div className="p-6">
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">
                  {getAnalysisTypeLabel(selectedAnalysis.type)}
                </h2>
                <span className={`px-3 py-1 text-sm font-medium rounded ${getAnalysisTypeColor(selectedAnalysis.type)}`}>
                  {getFileName(selectedAnalysis.fileId)}
                </span>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-medium mb-2">Résumé</h3>
                <p className="text-gray-700">{selectedAnalysis.summary}</p>
              </div>

              {selectedAnalysis.score && (
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">Score de qualité</span>
                    <span className="text-lg font-bold">{selectedAnalysis.score}/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        selectedAnalysis.score >= 80
                          ? 'bg-green-500'
                          : selectedAnalysis.score >= 60
                          ? 'bg-yellow-500'
                          : 'bg-red-500'
                      }`}
                      style={{ width: `${selectedAnalysis.score}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {/* Problèmes identifiés */}
            {selectedAnalysis.findings.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">
                  Problèmes identifiés ({selectedAnalysis.findings.length})
                </h3>
                <div className="space-y-4">
                  {selectedAnalysis.findings.map((finding) => (
                    <div
                      key={finding.id}
                      className={`border rounded-lg p-4 ${getSeverityColor(finding.severity)}`}
                    >
                      <div className="flex items-start space-x-3">
                        {getSeverityIcon(finding.severity)}
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{finding.title}</h4>
                            <span className={`px-2 py-1 text-xs font-medium rounded ${
                              finding.type === 'ERROR' ? 'bg-red-100 text-red-800' :
                              finding.type === 'WARNING' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {finding.type}
                            </span>
                          </div>
                          <p className="text-gray-700 mb-2">{finding.description}</p>
                          <div className="text-sm text-gray-600">
                            Ligne {finding.location.startLine}
                            {finding.location.endLine !== finding.location.startLine && 
                              ` - ${finding.location.endLine}`
                            }
                          </div>
                          {finding.code && (
                            <pre className="mt-2 bg-white p-2 rounded text-sm overflow-x-auto">
                              <code>{finding.code}</code>
                            </pre>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Suggestions d'amélioration */}
            {selectedAnalysis.suggestions.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-4">
                  Suggestions d'amélioration ({selectedAnalysis.suggestions.length})
                </h3>
                <div className="space-y-4">
                  {selectedAnalysis.suggestions.map((suggestion) => (
                    <div key={suggestion.id} className="border rounded-lg p-4 bg-white">
                      <div className="flex items-start space-x-3">
                        <Lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{suggestion.title}</h4>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 text-xs font-medium rounded ${
                                suggestion.impact === 'HIGH' ? 'bg-red-100 text-red-800' :
                                suggestion.impact === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                Impact {suggestion.impact}
                              </span>
                              <button
                                className={`px-3 py-1 text-xs font-medium rounded ${
                                  suggestion.approved
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                                }`}
                              >
                                {suggestion.approved ? 'Approuvé' : 'Approuver'}
                              </button>
                            </div>
                          </div>
                          <p className="text-gray-700 mb-3">{suggestion.description}</p>
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h5 className="font-medium text-sm mb-2">Code actuel:</h5>
                              <pre className="bg-red-50 p-2 rounded text-sm overflow-x-auto">
                                <code>{suggestion.originalCode}</code>
                              </pre>
                            </div>
                            <div>
                              <h5 className="font-medium text-sm mb-2">Code suggéré:</h5>
                              <pre className="bg-green-50 p-2 rounded text-sm overflow-x-auto">
                                <code>{suggestion.suggestedCode}</code>
                              </pre>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>Sélectionnez une analyse pour voir les détails</p>
          </div>
        )}
      </div>
    </div>
  );
}
