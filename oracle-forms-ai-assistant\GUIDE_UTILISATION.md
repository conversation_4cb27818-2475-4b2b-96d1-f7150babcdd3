# Guide d'utilisation - Oracle Forms AI Assistant

## 🚀 Démarrage rapide

### 1. Configuration initiale

1. **Obtenir une clé API Groq** (gratuite)
   - Rendez-vous sur [console.groq.com](https://console.groq.com)
   - Créez un compte gratuit
   - Générez une nouvelle clé API
   - Copiez la clé (elle commence par `gsk_...`)

2. **Configurer l'application**
   - Ouvrez l'application dans votre navigateur
   - Dans la bannière jaune en haut, collez votre clé API Groq
   - La bannière disparaîtra une fois la clé configurée

### 2. Chargement des fichiers Oracle Forms

#### Méthodes de chargement :
- **Glisser-déposer** : Faites glisser vos fichiers directement dans la zone de drop
- **Sélection manuelle** : Cliquez dans la zone pour ouvrir l'explorateur de fichiers

#### Formats supportés :
- **.fmb** - Fichiers Oracle Forms
- **.rdf** - Fichiers Oracle Reports  
- **.pll** - Bibliothèques PL/SQL
- **.olb** - Bibliothèques d'objets

#### Fichier d'exemple :
Un fichier d'exemple `example-form.fmb` est disponible dans le dossier `public/` pour tester l'application.

### 3. Navigation dans l'interface

L'interface est organisée en 4 onglets principaux :

#### 📁 Onglet "Fichiers"
- **Vue d'ensemble** : Liste de tous les fichiers chargés
- **Actions disponibles** :
  - Sélectionner un fichier (clic sur la carte)
  - Analyser le code (bouton "Analyser")
  - Générer la documentation (bouton "Documenter")
- **Informations affichées** : Nom, type, taille, date de modification

#### ✏️ Onglet "Éditeur"
- **Éditeur de code** : Monaco Editor avec coloration syntaxique PL/SQL
- **Sous-onglets** :
  - **Code Source** : Visualisation et édition du code complet
  - **Structure** : Vue hiérarchique des métadonnées, blocs et dépendances
  - **Triggers** : Liste détaillée de tous les triggers avec leur code
  - **Procédures** : Procédures et fonctions avec paramètres et code

#### 📊 Onglet "Analyse"
- **Liste des analyses** : Historique de toutes les analyses effectuées
- **Filtres** : Par type d'analyse (Revue de Code, Documentation, etc.)
- **Détails d'analyse** :
  - Résumé et score de qualité
  - Problèmes identifiés avec niveaux de sévérité
  - Suggestions d'amélioration avec code avant/après

#### 🤖 Onglet "Assistant IA"
- **Chat interactif** : Conversation en langage naturel avec l'IA
- **Contexte automatique** : L'IA a accès aux fichiers chargés
- **Questions suggérées** : Exemples de questions pour commencer

## 🔍 Types d'analyses disponibles

### 1. Revue de Code (CODE_REVIEW)
**Objectif** : Identifier les problèmes de qualité et les erreurs potentielles

**L'IA analyse** :
- Qualité du code et respect des meilleures pratiques
- Erreurs potentielles et bugs
- Problèmes de sécurité et vulnérabilités
- Performance et optimisation
- Maintenabilité et lisibilité
- Conformité aux standards Oracle Forms

### 2. Documentation (DOCUMENTATION)
**Objectif** : Générer une documentation technique complète

**L'IA génère** :
- Vue d'ensemble du formulaire/rapport
- Description détaillée des blocs et items
- Explication des triggers et procédures
- Flux de données et logique métier
- Instructions d'utilisation
- Notes techniques importantes

### 3. Refactorisation (REFACTORING)
**Objectif** : Proposer des améliorations structurelles

**L'IA identifie** :
- Code dupliqué à factoriser
- Procédures trop longues à diviser
- Logique métier à centraliser
- Améliorations de la structure du code
- Simplification des expressions complexes

### 4. Optimisation (OPTIMIZATION)
**Objectif** : Améliorer les performances

**L'IA propose** :
- Optimisation des requêtes SQL
- Utilisation efficace de la mémoire
- Optimisation des triggers
- Réduction des appels réseau
- Amélioration de l'expérience utilisateur

## 💬 Utilisation du chat IA

### Questions types que vous pouvez poser :

#### Analyse générale :
- "Peux-tu analyser la structure de mon formulaire ?"
- "Quels sont les principaux problèmes dans ce code ?"
- "Comment améliorer les performances de cette application ?"

#### Questions techniques spécifiques :
- "Explique-moi ce trigger WHEN-VALIDATE-ITEM"
- "Comment optimiser cette requête SQL ?"
- "Pourquoi ce code génère-t-il une erreur ?"

#### Meilleures pratiques :
- "Quelles sont les meilleures pratiques pour les triggers Oracle Forms ?"
- "Comment sécuriser mon application Oracle Forms ?"
- "Comment structurer mon code pour une meilleure maintenabilité ?"

#### Aide au développement :
- "Peux-tu m'aider à écrire un trigger de validation ?"
- "Comment implémenter une recherche dynamique ?"
- "Quels sont les patterns recommandés pour la gestion d'erreurs ?"

### Conseils pour de meilleures réponses :
1. **Soyez spécifique** : Plus votre question est précise, plus la réponse sera utile
2. **Fournissez du contexte** : Mentionnez le fichier ou la section de code concernée
3. **Posez des questions de suivi** : N'hésitez pas à demander des clarifications
4. **Utilisez le contexte** : L'IA a accès à vos fichiers chargés, référencez-les

## 🎯 Conseils d'utilisation

### Pour une analyse optimale :
1. **Chargez tous les fichiers liés** : L'IA peut mieux analyser avec le contexte complet
2. **Commencez par une revue de code** : Identifiez d'abord les problèmes majeurs
3. **Utilisez les suggestions** : Examinez et appliquez les améliorations proposées
4. **Documentez régulièrement** : Générez la documentation après les modifications

### Pour le chat IA :
1. **Commencez par les questions suggérées** : Elles vous donneront des idées
2. **Soyez conversationnel** : L'IA comprend le langage naturel
3. **Demandez des exemples** : "Peux-tu me montrer un exemple de code ?"
4. **Explorez différents aspects** : Sécurité, performance, maintenabilité, etc.

## 🔧 Résolution de problèmes

### Problèmes courants :

#### "L'analyse ne fonctionne pas"
- ✅ Vérifiez que votre clé API Groq est correctement configurée
- ✅ Assurez-vous qu'un fichier est sélectionné
- ✅ Vérifiez votre connexion internet

#### "Le fichier ne se charge pas"
- ✅ Vérifiez que le format est supporté (.fmb, .rdf, .pll, .olb)
- ✅ Assurez-vous que le fichier n'est pas corrompu
- ✅ Essayez avec un fichier plus petit

#### "Le chat ne répond pas"
- ✅ Vérifiez votre clé API Groq
- ✅ Attendez quelques secondes (l'IA peut prendre du temps)
- ✅ Reformulez votre question si nécessaire

#### "L'éditeur ne s'affiche pas correctement"
- ✅ Actualisez la page
- ✅ Vérifiez que JavaScript est activé
- ✅ Essayez un autre navigateur

### Limitations actuelles :
- Seuls les fichiers FMB sont entièrement supportés (RDF, PLL, OLB en développement)
- La sauvegarde des modifications n'est pas encore implémentée
- L'historique des analyses n'est pas persistant entre les sessions

## 📞 Support

Si vous rencontrez des problèmes :
1. Consultez ce guide d'utilisation
2. Vérifiez les messages d'erreur dans la console du navigateur (F12)
3. Ouvrez une issue sur le repository GitHub
4. Contactez l'équipe de développement

---

**Bon développement avec Oracle Forms AI Assistant ! 🚀**
