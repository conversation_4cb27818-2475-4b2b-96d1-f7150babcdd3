import { 
  OracleFormsFile, 
  ParsedOracleFile, 
  FileMetadata, 
  Block, 
  Trigger, 
  Procedure, 
  Function, 
  Variable, 
  Dependency 
} from '@/types/oracle-forms';

export abstract class BaseOracleParser {
  protected file: OracleFormsFile;

  constructor(file: OracleFormsFile) {
    this.file = file;
  }

  abstract parse(): Promise<ParsedOracleFile>;

  protected extractMetadata(): FileMetadata {
    // Implémentation de base pour extraire les métadonnées
    return {
      version: this.extractVersion(),
      formName: this.extractFormName(),
      description: this.extractDescription(),
      author: this.extractAuthor(),
      creationDate: this.extractCreationDate(),
      lastModified: this.file.lastModified
    };
  }

  protected extractVersion(): string {
    // Recherche de la version dans le contenu du fichier
    const versionMatch = this.file.content.match(/VERSION\s*=\s*['"]([^'"]+)['"]/i);
    return versionMatch ? versionMatch[1] : 'Unknown';
  }

  protected extractFormName(): string | undefined {
    // Recherche du nom du formulaire
    const formNameMatch = this.file.content.match(/FORM_NAME\s*=\s*['"]([^'"]+)['"]/i);
    return formNameMatch ? formNameMatch[1] : undefined;
  }

  protected extractDescription(): string | undefined {
    // Recherche de la description
    const descMatch = this.file.content.match(/DESCRIPTION\s*=\s*['"]([^'"]+)['"]/i);
    return descMatch ? descMatch[1] : undefined;
  }

  protected extractAuthor(): string | undefined {
    // Recherche de l'auteur
    const authorMatch = this.file.content.match(/AUTHOR\s*=\s*['"]([^'"]+)['"]/i);
    return authorMatch ? authorMatch[1] : undefined;
  }

  protected extractCreationDate(): Date | undefined {
    // Recherche de la date de création
    const dateMatch = this.file.content.match(/CREATION_DATE\s*=\s*['"]([^'"]+)['"]/i);
    return dateMatch ? new Date(dateMatch[1]) : undefined;
  }

  protected extractTriggers(content: string, level: 'FORM' | 'BLOCK' | 'ITEM', parentId?: string): Trigger[] {
    const triggers: Trigger[] = [];
    
    // Pattern pour identifier les triggers
    const triggerPattern = /TRIGGER\s+(\w+)\s*\n([\s\S]*?)END\s+TRIGGER/gi;
    let match;

    while ((match = triggerPattern.exec(content)) !== null) {
      const triggerName = match[1];
      const triggerCode = match[2].trim();
      
      triggers.push({
        id: `${parentId || 'form'}_trigger_${triggerName}_${Date.now()}`,
        name: triggerName,
        type: triggerName,
        code: triggerCode,
        level,
        parentId,
        lineNumber: this.getLineNumber(content, match.index)
      });
    }

    return triggers;
  }

  protected extractProcedures(content: string): Procedure[] {
    const procedures: Procedure[] = [];
    
    // Pattern pour identifier les procédures
    const procPattern = /PROCEDURE\s+(\w+)\s*\((.*?)\)\s*IS\s*([\s\S]*?)END\s+\1/gi;
    let match;

    while ((match = procPattern.exec(content)) !== null) {
      const procName = match[1];
      const paramString = match[2];
      const procCode = match[3].trim();
      
      procedures.push({
        id: `proc_${procName}_${Date.now()}`,
        name: procName,
        parameters: this.parseParameters(paramString),
        code: procCode,
        lineNumber: this.getLineNumber(content, match.index)
      });
    }

    return procedures;
  }

  protected extractFunctions(content: string): Function[] {
    const functions: Function[] = [];
    
    // Pattern pour identifier les fonctions
    const funcPattern = /FUNCTION\s+(\w+)\s*\((.*?)\)\s*RETURN\s+(\w+)\s*IS\s*([\s\S]*?)END\s+\1/gi;
    let match;

    while ((match = funcPattern.exec(content)) !== null) {
      const funcName = match[1];
      const paramString = match[2];
      const returnType = match[3];
      const funcCode = match[4].trim();
      
      functions.push({
        id: `func_${funcName}_${Date.now()}`,
        name: funcName,
        parameters: this.parseParameters(paramString),
        code: funcCode,
        returnType,
        lineNumber: this.getLineNumber(content, match.index)
      });
    }

    return functions;
  }

  protected extractVariables(content: string): Variable[] {
    const variables: Variable[] = [];
    
    // Pattern pour identifier les variables
    const varPattern = /(\w+)\s+(\w+(?:\(\d+\))?)\s*(?::=\s*([^;]+))?;/gi;
    let match;

    while ((match = varPattern.exec(content)) !== null) {
      const varName = match[1];
      const varType = match[2];
      const defaultValue = match[3]?.trim();
      
      variables.push({
        id: `var_${varName}_${Date.now()}`,
        name: varName,
        type: varType,
        scope: 'LOCAL', // Par défaut, peut être ajusté selon le contexte
        defaultValue,
        lineNumber: this.getLineNumber(content, match.index)
      });
    }

    return variables;
  }

  protected parseParameters(paramString: string) {
    if (!paramString.trim()) return [];
    
    return paramString.split(',').map(param => {
      const parts = param.trim().split(/\s+/);
      const name = parts[0];
      const mode = parts.includes('OUT') ? (parts.includes('IN') ? 'IN OUT' : 'OUT') : 'IN';
      const type = parts[parts.length - 1];
      
      return {
        name,
        type,
        mode: mode as 'IN' | 'OUT' | 'IN OUT'
      };
    });
  }

  protected getLineNumber(content: string, index: number): number {
    return content.substring(0, index).split('\n').length;
  }

  protected extractDependencies(content: string): Dependency[] {
    const dependencies: Dependency[] = [];
    
    // Pattern pour identifier les dépendances (INCLUDE, LIBRARY, etc.)
    const depPatterns = [
      /INCLUDE\s+['"]([^'"]+)['"]/gi,
      /LIBRARY\s+['"]([^'"]+)['"]/gi,
      /CALL_FORM\s*\(\s*['"]([^'"]+)['"]/gi
    ];

    depPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        dependencies.push({
          id: `dep_${match[1]}_${Date.now()}`,
          name: match[1],
          type: 'LIBRARY',
          required: true
        });
      }
    });

    return dependencies;
  }
}
