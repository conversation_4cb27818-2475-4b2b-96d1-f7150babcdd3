'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2, FileText } from 'lucide-react';
import { ChatMessage, OracleFormsFile, ParsedOracleFile } from '@/types/oracle-forms';
import { GroqAIService } from '@/lib/ai/groq-service';

interface AIChatProps {
  files: OracleFormsFile[];
  parsedFiles: ParsedOracleFile[];
  groqApiKey: string;
}

export function AIChat({ files, parsedFiles, groqApiKey }: AIChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'welcome',
      role: 'assistant',
      content: 'Bonjour ! Je suis votre assistant IA spécialisé en Oracle Forms. Je peux vous aider à analyser votre code, répondre à vos questions techniques, et suggérer des améliorations. Comment puis-je vous aider aujourd\'hui ?',
      timestamp: new Date(),
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    if (!groqApiKey) {
      alert('Veuillez configurer votre clé API Groq pour utiliser le chat');
      return;
    }

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date(),
      fileContext: files.map(f => f.name)
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const aiService = new GroqAIService(groqApiKey);
      const response = await aiService.chatWithAssistant(
        [...messages, userMessage],
        { files, parsedFiles }
      );

      const assistantMessage: ChatMessage = {
        id: `assistant_${Date.now()}`,
        role: 'assistant',
        content: response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Erreur lors du chat:', error);
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: 'Désolé, une erreur s\'est produite lors du traitement de votre message. Veuillez réessayer.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessage = (content: string) => {
    // Simple formatage pour les blocs de code
    const parts = content.split('```');
    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // C'est un bloc de code
        return (
          <pre key={index} className="bg-gray-100 p-3 rounded-lg my-2 overflow-x-auto">
            <code className="text-sm">{part}</code>
          </pre>
        );
      } else {
        // C'est du texte normal
        return (
          <div key={index} className="whitespace-pre-wrap">
            {part}
          </div>
        );
      }
    });
  };

  const suggestedQuestions = [
    "Peux-tu analyser la structure de mon formulaire ?",
    "Quelles sont les meilleures pratiques pour les triggers Oracle Forms ?",
    "Comment optimiser les performances de mes requêtes ?",
    "Peux-tu identifier les problèmes potentiels dans mon code ?",
    "Comment améliorer la sécurité de mon application ?"
  ];

  return (
    <div className="h-full flex flex-col bg-white">
      {/* En-tête */}
      <div className="border-b p-4 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Bot className="h-8 w-8 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold">Assistant IA Oracle Forms</h2>
              <p className="text-sm text-gray-600">
                {files.length > 0 
                  ? `${files.length} fichier(s) chargé(s) • Prêt à vous aider`
                  : 'Chargez des fichiers pour une assistance contextuelle'
                }
              </p>
            </div>
          </div>
          
          {files.length > 0 && (
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">
                Contexte: {files.map(f => f.name).join(', ')}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-3xl flex space-x-3 ${
                message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
              }`}
            >
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                message.role === 'user' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {message.role === 'user' ? (
                  <User className="h-4 w-4" />
                ) : (
                  <Bot className="h-4 w-4" />
                )}
              </div>
              
              <div className={`rounded-lg p-4 ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <div className="text-sm">
                  {formatMessage(message.content)}
                </div>
                <div className={`text-xs mt-2 ${
                  message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="max-w-3xl flex space-x-3">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center">
                <Bot className="h-4 w-4" />
              </div>
              <div className="bg-gray-100 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-gray-600">L'assistant réfléchit...</span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Questions suggérées (affichées seulement s'il n'y a que le message de bienvenue) */}
      {messages.length === 1 && (
        <div className="border-t p-4 bg-gray-50">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Questions suggérées :</h3>
          <div className="flex flex-wrap gap-2">
            {suggestedQuestions.map((question, index) => (
              <button
                key={index}
                onClick={() => setInputMessage(question)}
                className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {question}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Zone de saisie */}
      <div className="border-t p-4 bg-white">
        <div className="flex space-x-3">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={
                groqApiKey 
                  ? "Posez votre question sur Oracle Forms..." 
                  : "Configurez votre clé API Groq pour utiliser le chat"
              }
              disabled={!groqApiKey || isLoading}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
              rows={3}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || !groqApiKey || isLoading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span>Envoyer</span>
          </button>
        </div>
        
        <div className="mt-2 text-xs text-gray-500">
          Appuyez sur Entrée pour envoyer, Shift+Entrée pour une nouvelle ligne
        </div>
      </div>
    </div>
  );
}
