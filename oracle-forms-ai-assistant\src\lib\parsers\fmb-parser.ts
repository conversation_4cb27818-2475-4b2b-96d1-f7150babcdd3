import { BaseOracleParser } from './base-parser';
import { ParsedOracleFile, Block, Item } from '@/types/oracle-forms';

export class FMBParser extends BaseOracleParser {
  async parse(): Promise<ParsedOracleFile> {
    // Vérifier si le fichier est binaire
    if (this.isBinaryFile()) {
      return this.parseBinaryFMB();
    }

    // Parser pour fichiers texte (comme notre exemple)
    const metadata = this.extractMetadata();
    const blocks = this.extractBlocks();
    const triggers = this.extractTriggers(this.file.content, 'FORM');
    const procedures = this.extractProcedures(this.file.content);
    const functions = this.extractFunctions(this.file.content);
    const variables = this.extractVariables(this.file.content);
    const dependencies = this.extractDependencies(this.file.content);

    return {
      metadata,
      blocks,
      triggers,
      procedures,
      functions,
      variables,
      dependencies
    };
  }

  private isBinaryFile(): boolean {
    // Détecter si le fichier contient des caractères binaires
    const content = this.file.content;

    // Vérifier la signature Oracle Forms
    if (content.startsWith('ROS.')) {
      return true;
    }

    // Vérifier la présence de caractères de contrôle non-imprimables
    const binaryPattern = /[\x00-\x08\x0E-\x1F\x7F-\xFF]/;
    const firstKB = content.substring(0, 1024);
    const binaryRatio = (firstKB.match(binaryPattern) || []).length / firstKB.length;

    return binaryRatio > 0.3; // Si plus de 30% de caractères binaires
  }

  private parseBinaryFMB(): ParsedOracleFile {
    // Parser basique pour fichiers binaires Oracle Forms
    const extractedData = this.extractFromBinaryFMB();

    return {
      metadata: {
        version: extractedData.version || 'Binaire Oracle Forms',
        formName: extractedData.formName || this.file.name.replace('.fmb', ''),
        description: 'Fichier binaire Oracle Forms - Parsing limité',
        author: 'Inconnu',
        creationDate: this.file.lastModified,
        lastModified: this.file.lastModified
      },
      blocks: extractedData.blocks || [],
      triggers: extractedData.triggers || [],
      procedures: extractedData.procedures || [],
      functions: extractedData.functions || [],
      variables: extractedData.variables || [],
      dependencies: extractedData.dependencies || []
    };
  }

  private extractFromBinaryFMB(): any {
    const content = this.file.content;
    const result: any = {
      version: null,
      formName: null,
      blocks: [],
      triggers: [],
      procedures: [],
      functions: [],
      variables: [],
      dependencies: []
    };

    try {
      // Extraire les chaînes de texte lisibles du fichier binaire
      const textStrings = this.extractReadableStrings(content);

      // Rechercher des patterns Oracle Forms dans les chaînes extraites
      result.triggers = this.findTriggersInStrings(textStrings);
      result.blocks = this.findBlocksInStrings(textStrings);
      result.procedures = this.findProceduresInStrings(textStrings);

      // Essayer d'extraire le nom du formulaire
      const formNameMatch = textStrings.find(str =>
        str.length > 3 && str.length < 50 && /^[A-Z][A-Z0-9_]*$/.test(str)
      );
      if (formNameMatch) {
        result.formName = formNameMatch;
      }

    } catch (error) {
      console.warn('Erreur lors de l\'extraction des données binaires:', error);
    }

    return result;
  }

  private extractReadableStrings(content: string): string[] {
    // Extraire les chaînes de caractères lisibles d'au moins 4 caractères
    const strings: string[] = [];
    const regex = /[a-zA-Z0-9_\-\.]{4,}/g;
    let match;

    while ((match = regex.exec(content)) !== null) {
      const str = match[0];
      if (str.length >= 4 && str.length <= 100) {
        strings.push(str);
      }
    }

    return [...new Set(strings)]; // Supprimer les doublons
  }

  private findTriggersInStrings(strings: string[]): any[] {
    const triggers: any[] = [];
    const triggerPatterns = [
      'WHEN-NEW-FORM-INSTANCE',
      'WHEN-BUTTON-PRESSED',
      'WHEN-VALIDATE-ITEM',
      'PRE-QUERY',
      'POST-QUERY',
      'KEY-EXIT',
      'WHEN-MOUSE-CLICK'
    ];

    strings.forEach(str => {
      triggerPatterns.forEach(pattern => {
        if (str.includes(pattern)) {
          triggers.push({
            id: `trigger_${pattern}_${Date.now()}`,
            name: pattern,
            type: pattern,
            code: `-- Code extrait du fichier binaire\n-- Trigger: ${pattern}`,
            level: 'FORM',
            lineNumber: 0
          });
        }
      });
    });

    return triggers;
  }

  private findBlocksInStrings(strings: string[]): any[] {
    const blocks: any[] = [];

    // Rechercher des noms de blocs potentiels
    const blockCandidates = strings.filter(str =>
      str.length > 3 &&
      str.length < 30 &&
      /^[A-Z][A-Z0-9_]*$/.test(str) &&
      !str.includes('WHEN') &&
      !str.includes('KEY')
    );

    blockCandidates.slice(0, 10).forEach((blockName, index) => {
      blocks.push({
        id: `block_${blockName}_${Date.now()}`,
        name: blockName,
        type: index === 0 ? 'DATA_BLOCK' : 'CONTROL_BLOCK',
        items: [],
        triggers: [],
        properties: {}
      });
    });

    return blocks;
  }

  private findProceduresInStrings(strings: string[]): any[] {
    const procedures: any[] = [];

    // Rechercher des mots-clés PL/SQL
    const plsqlKeywords = strings.filter(str =>
      ['BEGIN', 'END', 'DECLARE', 'PROCEDURE', 'FUNCTION'].includes(str.toUpperCase())
    );

    if (plsqlKeywords.length > 0) {
      procedures.push({
        id: `proc_extracted_${Date.now()}`,
        name: 'Code_PL_SQL_Extrait',
        parameters: [],
        code: `-- Code PL/SQL détecté dans le fichier binaire\n-- Mots-clés trouvés: ${plsqlKeywords.join(', ')}`,
        lineNumber: 0
      });
    }

    return procedures;
  }

  private extractBlocks(): Block[] {
    const blocks: Block[] = [];

    // Pattern pour identifier les blocs dans un fichier FMB
    const blockPattern = /BLOCK\s+(\w+)\s*\{([\s\S]*?)\}/gi;
    let match;

    while ((match = blockPattern.exec(this.file.content)) !== null) {
      const blockName = match[1];
      const blockContent = match[2];

      const block: Block = {
        id: `block_${blockName}_${Date.now()}`,
        name: blockName,
        type: this.determineBlockType(blockContent),
        tableName: this.extractTableName(blockContent),
        items: this.extractItems(blockContent, blockName),
        triggers: this.extractTriggers(blockContent, 'BLOCK', blockName),
        properties: this.extractBlockProperties(blockContent)
      };

      blocks.push(block);
    }

    return blocks;
  }

  private determineBlockType(blockContent: string): 'DATA_BLOCK' | 'CONTROL_BLOCK' {
    // Détermine si c'est un bloc de données ou de contrôle
    if (blockContent.includes('TABLE_NAME') || blockContent.includes('QUERY_DATA_SOURCE_NAME')) {
      return 'DATA_BLOCK';
    }
    return 'CONTROL_BLOCK';
  }

  private extractTableName(blockContent: string): string | undefined {
    const tableMatch = blockContent.match(/TABLE_NAME\s*=\s*['"]([^'"]+)['"]/i);
    return tableMatch ? tableMatch[1] : undefined;
  }

  private extractItems(blockContent: string, blockName: string): Item[] {
    const items: Item[] = [];

    // Pattern pour identifier les items dans un bloc
    const itemPattern = /ITEM\s+(\w+)\s*\{([\s\S]*?)\}/gi;
    let match;

    while ((match = itemPattern.exec(blockContent)) !== null) {
      const itemName = match[1];
      const itemContent = match[2];

      const item: Item = {
        id: `item_${blockName}_${itemName}_${Date.now()}`,
        name: itemName,
        type: this.determineItemType(itemContent),
        dataType: this.extractDataType(itemContent),
        maxLength: this.extractMaxLength(itemContent),
        required: this.isRequired(itemContent),
        defaultValue: this.extractDefaultValue(itemContent),
        triggers: this.extractTriggers(itemContent, 'ITEM', itemName),
        properties: this.extractItemProperties(itemContent)
      };

      items.push(item);
    }

    return items;
  }

  private determineItemType(itemContent: string): Item['type'] {
    if (itemContent.includes('ITEM_TYPE = TEXT_ITEM')) return 'TEXT_ITEM';
    if (itemContent.includes('ITEM_TYPE = DISPLAY_ITEM')) return 'DISPLAY_ITEM';
    if (itemContent.includes('ITEM_TYPE = LIST_ITEM')) return 'LIST_ITEM';
    if (itemContent.includes('ITEM_TYPE = CHECKBOX')) return 'CHECKBOX';
    if (itemContent.includes('ITEM_TYPE = RADIO_GROUP')) return 'RADIO_GROUP';
    if (itemContent.includes('ITEM_TYPE = BUTTON')) return 'BUTTON';
    return 'TEXT_ITEM'; // Par défaut
  }

  private extractDataType(itemContent: string): string | undefined {
    const dataTypeMatch = itemContent.match(/DATA_TYPE\s*=\s*['"]?([^'"\s]+)['"]?/i);
    return dataTypeMatch ? dataTypeMatch[1] : undefined;
  }

  private extractMaxLength(itemContent: string): number | undefined {
    const maxLengthMatch = itemContent.match(/MAX_LENGTH\s*=\s*(\d+)/i);
    return maxLengthMatch ? parseInt(maxLengthMatch[1]) : undefined;
  }

  private isRequired(itemContent: string): boolean {
    const requiredMatch = itemContent.match(/REQUIRED\s*=\s*(TRUE|YES|1)/i);
    return !!requiredMatch;
  }

  private extractDefaultValue(itemContent: string): string | undefined {
    const defaultMatch = itemContent.match(/DEFAULT_VALUE\s*=\s*['"]([^'"]+)['"]/i);
    return defaultMatch ? defaultMatch[1] : undefined;
  }

  private extractBlockProperties(blockContent: string): Record<string, any> {
    const properties: Record<string, any> = {};

    // Extraction des propriétés communes des blocs
    const propertyPatterns = [
      { key: 'QUERY_DATA_SOURCE_NAME', pattern: /QUERY_DATA_SOURCE_NAME\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'WHERE_CLAUSE', pattern: /WHERE_CLAUSE\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'ORDER_BY_CLAUSE', pattern: /ORDER_BY_CLAUSE\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'RECORDS_DISPLAYED', pattern: /RECORDS_DISPLAYED\s*=\s*(\d+)/i },
      { key: 'RECORDS_BUFFERED', pattern: /RECORDS_BUFFERED\s*=\s*(\d+)/i }
    ];

    propertyPatterns.forEach(({ key, pattern }) => {
      const match = blockContent.match(pattern);
      if (match) {
        properties[key] = isNaN(Number(match[1])) ? match[1] : Number(match[1]);
      }
    });

    return properties;
  }

  private extractItemProperties(itemContent: string): Record<string, any> {
    const properties: Record<string, any> = {};

    // Extraction des propriétés communes des items
    const propertyPatterns = [
      { key: 'X_POSITION', pattern: /X_POSITION\s*=\s*(\d+)/i },
      { key: 'Y_POSITION', pattern: /Y_POSITION\s*=\s*(\d+)/i },
      { key: 'WIDTH', pattern: /WIDTH\s*=\s*(\d+)/i },
      { key: 'HEIGHT', pattern: /HEIGHT\s*=\s*(\d+)/i },
      { key: 'PROMPT', pattern: /PROMPT\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'TOOLTIP', pattern: /TOOLTIP\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'ENABLED', pattern: /ENABLED\s*=\s*(TRUE|FALSE|YES|NO)/i },
      { key: 'VISIBLE', pattern: /VISIBLE\s*=\s*(TRUE|FALSE|YES|NO)/i }
    ];

    propertyPatterns.forEach(({ key, pattern }) => {
      const match = itemContent.match(pattern);
      if (match) {
        if (key === 'ENABLED' || key === 'VISIBLE') {
          properties[key] = /TRUE|YES/i.test(match[1]);
        } else {
          properties[key] = isNaN(Number(match[1])) ? match[1] : Number(match[1]);
        }
      }
    });

    return properties;
  }
}
