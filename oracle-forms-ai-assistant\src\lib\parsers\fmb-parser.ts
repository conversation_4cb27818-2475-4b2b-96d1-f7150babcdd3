import { BaseOracleParser } from './base-parser';
import { ParsedOracleFile, Block, Item } from '@/types/oracle-forms';

export class FMBParser extends BaseOracleParser {
  async parse(): Promise<ParsedOracleFile> {
    const metadata = this.extractMetadata();
    const blocks = this.extractBlocks();
    const triggers = this.extractTriggers(this.file.content, 'FORM');
    const procedures = this.extractProcedures(this.file.content);
    const functions = this.extractFunctions(this.file.content);
    const variables = this.extractVariables(this.file.content);
    const dependencies = this.extractDependencies(this.file.content);

    return {
      metadata,
      blocks,
      triggers,
      procedures,
      functions,
      variables,
      dependencies
    };
  }

  private extractBlocks(): Block[] {
    const blocks: Block[] = [];
    
    // Pattern pour identifier les blocs dans un fichier FMB
    const blockPattern = /BLOCK\s+(\w+)\s*\{([\s\S]*?)\}/gi;
    let match;

    while ((match = blockPattern.exec(this.file.content)) !== null) {
      const blockName = match[1];
      const blockContent = match[2];
      
      const block: Block = {
        id: `block_${blockName}_${Date.now()}`,
        name: blockName,
        type: this.determineBlockType(blockContent),
        tableName: this.extractTableName(blockContent),
        items: this.extractItems(blockContent, blockName),
        triggers: this.extractTriggers(blockContent, 'BLOCK', blockName),
        properties: this.extractBlockProperties(blockContent)
      };

      blocks.push(block);
    }

    return blocks;
  }

  private determineBlockType(blockContent: string): 'DATA_BLOCK' | 'CONTROL_BLOCK' {
    // Détermine si c'est un bloc de données ou de contrôle
    if (blockContent.includes('TABLE_NAME') || blockContent.includes('QUERY_DATA_SOURCE_NAME')) {
      return 'DATA_BLOCK';
    }
    return 'CONTROL_BLOCK';
  }

  private extractTableName(blockContent: string): string | undefined {
    const tableMatch = blockContent.match(/TABLE_NAME\s*=\s*['"]([^'"]+)['"]/i);
    return tableMatch ? tableMatch[1] : undefined;
  }

  private extractItems(blockContent: string, blockName: string): Item[] {
    const items: Item[] = [];
    
    // Pattern pour identifier les items dans un bloc
    const itemPattern = /ITEM\s+(\w+)\s*\{([\s\S]*?)\}/gi;
    let match;

    while ((match = itemPattern.exec(blockContent)) !== null) {
      const itemName = match[1];
      const itemContent = match[2];
      
      const item: Item = {
        id: `item_${blockName}_${itemName}_${Date.now()}`,
        name: itemName,
        type: this.determineItemType(itemContent),
        dataType: this.extractDataType(itemContent),
        maxLength: this.extractMaxLength(itemContent),
        required: this.isRequired(itemContent),
        defaultValue: this.extractDefaultValue(itemContent),
        triggers: this.extractTriggers(itemContent, 'ITEM', itemName),
        properties: this.extractItemProperties(itemContent)
      };

      items.push(item);
    }

    return items;
  }

  private determineItemType(itemContent: string): Item['type'] {
    if (itemContent.includes('ITEM_TYPE = TEXT_ITEM')) return 'TEXT_ITEM';
    if (itemContent.includes('ITEM_TYPE = DISPLAY_ITEM')) return 'DISPLAY_ITEM';
    if (itemContent.includes('ITEM_TYPE = LIST_ITEM')) return 'LIST_ITEM';
    if (itemContent.includes('ITEM_TYPE = CHECKBOX')) return 'CHECKBOX';
    if (itemContent.includes('ITEM_TYPE = RADIO_GROUP')) return 'RADIO_GROUP';
    if (itemContent.includes('ITEM_TYPE = BUTTON')) return 'BUTTON';
    return 'TEXT_ITEM'; // Par défaut
  }

  private extractDataType(itemContent: string): string | undefined {
    const dataTypeMatch = itemContent.match(/DATA_TYPE\s*=\s*['"]?([^'"\s]+)['"]?/i);
    return dataTypeMatch ? dataTypeMatch[1] : undefined;
  }

  private extractMaxLength(itemContent: string): number | undefined {
    const maxLengthMatch = itemContent.match(/MAX_LENGTH\s*=\s*(\d+)/i);
    return maxLengthMatch ? parseInt(maxLengthMatch[1]) : undefined;
  }

  private isRequired(itemContent: string): boolean {
    const requiredMatch = itemContent.match(/REQUIRED\s*=\s*(TRUE|YES|1)/i);
    return !!requiredMatch;
  }

  private extractDefaultValue(itemContent: string): string | undefined {
    const defaultMatch = itemContent.match(/DEFAULT_VALUE\s*=\s*['"]([^'"]+)['"]/i);
    return defaultMatch ? defaultMatch[1] : undefined;
  }

  private extractBlockProperties(blockContent: string): Record<string, any> {
    const properties: Record<string, any> = {};
    
    // Extraction des propriétés communes des blocs
    const propertyPatterns = [
      { key: 'QUERY_DATA_SOURCE_NAME', pattern: /QUERY_DATA_SOURCE_NAME\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'WHERE_CLAUSE', pattern: /WHERE_CLAUSE\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'ORDER_BY_CLAUSE', pattern: /ORDER_BY_CLAUSE\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'RECORDS_DISPLAYED', pattern: /RECORDS_DISPLAYED\s*=\s*(\d+)/i },
      { key: 'RECORDS_BUFFERED', pattern: /RECORDS_BUFFERED\s*=\s*(\d+)/i }
    ];

    propertyPatterns.forEach(({ key, pattern }) => {
      const match = blockContent.match(pattern);
      if (match) {
        properties[key] = isNaN(Number(match[1])) ? match[1] : Number(match[1]);
      }
    });

    return properties;
  }

  private extractItemProperties(itemContent: string): Record<string, any> {
    const properties: Record<string, any> = {};
    
    // Extraction des propriétés communes des items
    const propertyPatterns = [
      { key: 'X_POSITION', pattern: /X_POSITION\s*=\s*(\d+)/i },
      { key: 'Y_POSITION', pattern: /Y_POSITION\s*=\s*(\d+)/i },
      { key: 'WIDTH', pattern: /WIDTH\s*=\s*(\d+)/i },
      { key: 'HEIGHT', pattern: /HEIGHT\s*=\s*(\d+)/i },
      { key: 'PROMPT', pattern: /PROMPT\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'TOOLTIP', pattern: /TOOLTIP\s*=\s*['"]([^'"]+)['"]/i },
      { key: 'ENABLED', pattern: /ENABLED\s*=\s*(TRUE|FALSE|YES|NO)/i },
      { key: 'VISIBLE', pattern: /VISIBLE\s*=\s*(TRUE|FALSE|YES|NO)/i }
    ];

    propertyPatterns.forEach(({ key, pattern }) => {
      const match = itemContent.match(pattern);
      if (match) {
        if (key === 'ENABLED' || key === 'VISIBLE') {
          properties[key] = /TRUE|YES/i.test(match[1]);
        } else {
          properties[key] = isNaN(Number(match[1])) ? match[1] : Number(match[1]);
        }
      }
    });

    return properties;
  }
}
