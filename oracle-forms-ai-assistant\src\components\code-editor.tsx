'use client';

import { useState, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { OracleFormsFile, ParsedOracleFile } from '@/types/oracle-forms';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface CodeEditorProps {
  file: OracleFormsFile;
  parsedFile?: ParsedOracleFile;
}

export function CodeEditor({ file, parsedFile }: CodeEditorProps) {
  const [code, setCode] = useState(file.content);
  const [selectedTab, setSelectedTab] = useState('code');

  useEffect(() => {
    setCode(file.content);
  }, [file.content]);

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      setCode(value);
    }
  };

  const getLanguage = (fileType: string) => {
    switch (fileType) {
      case 'FMB':
      case 'RDF':
      case 'PLL':
        return 'sql'; // PL/SQL est proche de SQL pour la coloration syntaxique
      case 'OLB':
        return 'sql';
      default:
        return 'plaintext';
    }
  };

  const isBinaryFile = () => {
    return file.content.startsWith('ROS.') || /[\x00-\x08\x0E-\x1F\x7F-\xFF]/.test(file.content.substring(0, 100));
  };

  const getDisplayContent = () => {
    if (isBinaryFile()) {
      return `-- FICHIER BINAIRE ORACLE FORMS --
-- Nom: ${file.name}
-- Taille: ${Math.round(file.size / 1024)} KB
-- Type: ${file.type}
--
-- Ce fichier est un fichier binaire Oracle Forms (.fmb) compilé.
-- L'affichage du contenu brut n'est pas possible.
--
-- Pour voir le code source, vous devez :
-- 1. Ouvrir le fichier dans Oracle Forms Builder
-- 2. Exporter en format texte (.fmt)
-- 3. Ou extraire le code PL/SQL des triggers et procédures
--
-- L'analyse IA peut néanmoins extraire certaines informations
-- à partir des chaînes de caractères présentes dans le fichier.

-- INFORMATIONS EXTRAITES :
${parsedFile ? `
-- Blocs détectés: ${parsedFile.blocks.length}
-- Triggers détectés: ${parsedFile.triggers.length}
-- Procédures détectées: ${parsedFile.procedures.length}
` : '-- Aucune information extraite disponible'}

-- Pour une analyse complète, utilisez un fichier source (.fmt, .pll, .sql)`;
    }
    return code;
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">{file.name}</h2>
          <p className="text-sm text-gray-500">
            {file.type} • {Math.round(file.size / 1024)} KB
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">
            {file.type}
          </span>
          <button className="px-3 py-1 text-sm font-medium text-blue-600 hover:text-blue-800">
            Sauvegarder
          </button>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1 flex flex-col">
        <TabsList className="w-full justify-start border-b rounded-none bg-gray-50">
          <TabsTrigger value="code">Code Source</TabsTrigger>
          {parsedFile && (
            <>
              <TabsTrigger value="structure">Structure</TabsTrigger>
              <TabsTrigger value="triggers">Triggers</TabsTrigger>
              <TabsTrigger value="procedures">Procédures</TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="code" className="flex-1 mt-0">
          {isBinaryFile() ? (
            <div className="h-full bg-gray-50 p-4">
              <div className="bg-yellow-100 border border-yellow-400 rounded-lg p-4 mb-4">
                <div className="flex items-center">
                  <span className="text-yellow-600 mr-2">⚠️</span>
                  <div>
                    <h3 className="font-medium text-yellow-800">Fichier binaire détecté</h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      Ce fichier Oracle Forms est en format binaire. L'affichage et l'édition du code source ne sont pas possibles directement.
                    </p>
                  </div>
                </div>
              </div>
              <Editor
                height="calc(100% - 100px)"
                language="sql"
                value={getDisplayContent()}
                theme="vs-light"
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                  fontSize: 14,
                  lineNumbers: 'on',
                  wordWrap: 'on',
                  automaticLayout: true,
                  scrollBeyondLastLine: false,
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                }}
              />
            </div>
          ) : (
            <Editor
              height="100%"
              language={getLanguage(file.type)}
              value={code}
              onChange={handleEditorChange}
              theme="vs-light"
              options={{
                minimap: { enabled: true },
                fontSize: 14,
                lineNumbers: 'on',
                wordWrap: 'on',
                automaticLayout: true,
                scrollBeyondLastLine: false,
                readOnly: false,
                folding: true,
                lineDecorationsWidth: 10,
                lineNumbersMinChars: 4,
                glyphMargin: true,
                contextmenu: true,
                mouseWheelZoom: true,
                cursorBlinking: 'blink',
                cursorStyle: 'line',
                renderWhitespace: 'selection',
                renderControlCharacters: false,
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                tabSize: 2,
                insertSpaces: true,
                detectIndentation: true,
                trimAutoWhitespace: true,
                formatOnPaste: true,
                formatOnType: true
              }}
            />
          )}
        </TabsContent>

        {parsedFile && (
          <>
            <TabsContent value="structure" className="flex-1 mt-0 p-4 overflow-auto">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Métadonnées</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <div><strong>Version:</strong> {parsedFile.metadata.version}</div>
                    {parsedFile.metadata.formName && (
                      <div><strong>Nom du formulaire:</strong> {parsedFile.metadata.formName}</div>
                    )}
                    {parsedFile.metadata.description && (
                      <div><strong>Description:</strong> {parsedFile.metadata.description}</div>
                    )}
                    {parsedFile.metadata.author && (
                      <div><strong>Auteur:</strong> {parsedFile.metadata.author}</div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">Blocs ({parsedFile.blocks.length})</h3>
                  <div className="space-y-3">
                    {parsedFile.blocks.map((block) => (
                      <div key={block.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{block.name}</h4>
                          <span className={`px-2 py-1 text-xs rounded ${
                            block.type === 'DATA_BLOCK'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {block.type}
                          </span>
                        </div>
                        {block.tableName && (
                          <p className="text-sm text-gray-600 mb-2">
                            Table: {block.tableName}
                          </p>
                        )}
                        <p className="text-sm text-gray-500">
                          {block.items.length} items, {block.triggers.length} triggers
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">Dépendances ({parsedFile.dependencies.length})</h3>
                  <div className="space-y-2">
                    {parsedFile.dependencies.map((dep) => (
                      <div key={dep.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <span className="font-medium">{dep.name}</span>
                        <span className={`px-2 py-1 text-xs rounded ${
                          dep.required ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {dep.required ? 'Requis' : 'Optionnel'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="triggers" className="flex-1 mt-0 p-4 overflow-auto">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Triggers ({parsedFile.triggers.length})</h3>
                {parsedFile.triggers.map((trigger) => (
                  <div key={trigger.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{trigger.name}</h4>
                      <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded">
                        {trigger.level}
                      </span>
                    </div>
                    <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                      <code>{trigger.code}</code>
                    </pre>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="procedures" className="flex-1 mt-0 p-4 overflow-auto">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">
                  Procédures et Fonctions ({parsedFile.procedures.length + parsedFile.functions.length})
                </h3>

                {parsedFile.procedures.map((proc) => (
                  <div key={proc.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{proc.name}</h4>
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                        PROCEDURE
                      </span>
                    </div>
                    {proc.parameters.length > 0 && (
                      <div className="mb-2">
                        <strong className="text-sm">Paramètres:</strong>
                        <ul className="text-sm text-gray-600 ml-4">
                          {proc.parameters.map((param, index) => (
                            <li key={index}>
                              {param.name} ({param.mode}) {param.type}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                      <code>{proc.code}</code>
                    </pre>
                  </div>
                ))}

                {parsedFile.functions.map((func) => (
                  <div key={func.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{func.name}</h4>
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        FUNCTION → {func.returnType}
                      </span>
                    </div>
                    {func.parameters.length > 0 && (
                      <div className="mb-2">
                        <strong className="text-sm">Paramètres:</strong>
                        <ul className="text-sm text-gray-600 ml-4">
                          {func.parameters.map((param, index) => (
                            <li key={index}>
                              {param.name} ({param.mode}) {param.type}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                      <code>{func.code}</code>
                    </pre>
                  </div>
                ))}
              </div>
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  );
}
