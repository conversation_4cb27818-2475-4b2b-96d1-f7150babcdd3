// Script de vérification du modèle Groq
// Usage: node check-model.js YOUR_GROQ_API_KEY

const Groq = require('groq-sdk');

async function checkGroqModel(apiKey) {
  if (!apiKey) {
    console.error('❌ Veuillez fournir une clé API Groq');
    console.log('Usage: node check-model.js YOUR_GROQ_API_KEY');
    process.exit(1);
  }

  const groq = new Groq({ apiKey });
  const modelName = 'meta-llama/llama-4-scout-17b-16e-instruct';

  console.log('🔍 Vérification du modèle Groq...');
  console.log(`📋 Modèle testé: ${modelName}`);
  console.log('⏳ Test en cours...\n');

  try {
    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "Tu es un assistant IA expert en Oracle Forms. Réponds en français."
        },
        {
          role: "user",
          content: "Bonjour ! Peux-tu me confirmer que tu fonctionnes correctement et me dire quelques mots sur Oracle Forms ?"
        }
      ],
      model: modelName,
      temperature: 0.3,
      max_tokens: 200
    });

    const response = completion.choices[0]?.message?.content;
    
    if (response) {
      console.log('✅ Modèle fonctionnel !');
      console.log('📝 Réponse du modèle:');
      console.log('─'.repeat(50));
      console.log(response);
      console.log('─'.repeat(50));
      console.log('\n🎉 Le modèle Llama 4 Scout est prêt pour Oracle Forms AI Assistant !');
    } else {
      console.log('❌ Aucune réponse reçue du modèle');
    }

  } catch (error) {
    console.error('❌ Erreur lors du test du modèle:');
    console.error(error.message);
    
    if (error.message.includes('model_decommissioned')) {
      console.log('\n💡 Le modèle a été décommissionné. Modèles alternatifs suggérés:');
      console.log('   - llama-3.3-70b-versatile');
      console.log('   - mixtral-8x7b-32768');
      console.log('   - gemma2-9b-it');
    } else if (error.message.includes('invalid_api_key')) {
      console.log('\n💡 Clé API invalide. Vérifiez votre clé sur https://console.groq.com');
    } else if (error.message.includes('rate_limit')) {
      console.log('\n💡 Limite de taux atteinte. Attendez quelques minutes et réessayez.');
    }
  }
}

// Récupérer la clé API depuis les arguments de ligne de commande
const apiKey = process.argv[2];
checkGroqModel(apiKey);
