// Types pour les fichiers Oracle Forms

export interface OracleFormsFile {
  id: string;
  name: string;
  type: 'FMB' | 'RDF' | 'PLL' | 'OLB';
  content: string;
  size: number;
  lastModified: Date;
  parsed?: ParsedOracleFile;
}

export interface ParsedOracleFile {
  metadata: FileMetadata;
  blocks: Block[];
  triggers: Trigger[];
  procedures: Procedure[];
  functions: Function[];
  variables: Variable[];
  dependencies: Dependency[];
}

export interface FileMetadata {
  version: string;
  formName?: string;
  reportName?: string;
  libraryName?: string;
  description?: string;
  author?: string;
  creationDate?: Date;
  lastModified?: Date;
}

export interface Block {
  id: string;
  name: string;
  type: 'DATA_BLOCK' | 'CONTROL_BLOCK';
  tableName?: string;
  items: Item[];
  triggers: Trigger[];
  properties: Record<string, any>;
}

export interface Item {
  id: string;
  name: string;
  type: 'TEXT_ITEM' | 'DISPLAY_ITEM' | 'LIST_ITEM' | 'CHECKBOX' | 'RADIO_GROUP' | 'BUTTON';
  dataType?: string;
  maxLength?: number;
  required?: boolean;
  defaultValue?: string;
  triggers: Trigger[];
  properties: Record<string, any>;
}

export interface Trigger {
  id: string;
  name: string;
  type: string;
  code: string;
  level: 'FORM' | 'BLOCK' | 'ITEM';
  parentId?: string;
  lineNumber?: number;
}

export interface Procedure {
  id: string;
  name: string;
  parameters: Parameter[];
  code: string;
  returnType?: string;
  lineNumber?: number;
}

export interface Function {
  id: string;
  name: string;
  parameters: Parameter[];
  code: string;
  returnType: string;
  lineNumber?: number;
}

export interface Parameter {
  name: string;
  type: string;
  mode: 'IN' | 'OUT' | 'IN OUT';
  defaultValue?: string;
}

export interface Variable {
  id: string;
  name: string;
  type: string;
  scope: 'GLOBAL' | 'LOCAL';
  defaultValue?: string;
  lineNumber?: number;
}

export interface Dependency {
  id: string;
  name: string;
  type: 'LIBRARY' | 'FORM' | 'REPORT' | 'DATABASE_OBJECT';
  path?: string;
  required: boolean;
}

// Types pour l'analyse IA
export interface AIAnalysis {
  id: string;
  fileId: string;
  timestamp: Date;
  type: 'CODE_REVIEW' | 'DOCUMENTATION' | 'REFACTORING' | 'OPTIMIZATION';
  summary: string;
  findings: Finding[];
  suggestions: Suggestion[];
  score?: number;
}

export interface Finding {
  id: string;
  type: 'ERROR' | 'WARNING' | 'INFO' | 'SUGGESTION';
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  title: string;
  description: string;
  location: CodeLocation;
  code?: string;
}

export interface Suggestion {
  id: string;
  type: 'REFACTOR' | 'OPTIMIZE' | 'DOCUMENT' | 'FIX';
  title: string;
  description: string;
  location: CodeLocation;
  originalCode: string;
  suggestedCode: string;
  impact: 'HIGH' | 'MEDIUM' | 'LOW';
  approved?: boolean;
}

export interface CodeLocation {
  startLine: number;
  endLine: number;
  startColumn?: number;
  endColumn?: number;
  fileName?: string;
}

// Types pour le chat IA
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  fileContext?: string[];
  codeSnippets?: CodeSnippet[];
}

export interface CodeSnippet {
  id: string;
  fileName: string;
  code: string;
  language: string;
  startLine: number;
  endLine: number;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  fileIds: string[];
}
